package ai

import (
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"image/png"
	"io"
	"math"
	"math/rand"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/disintegration/imaging"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/upload"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AutoImageTextService struct{}

// SubmitAutoImageTextTask 提交批量图文生成任务
func (s *AutoImageTextService) SubmitAutoImageTextTask(params request.AutoImageTextTaskParams, userId uint) (string, error) {
	// 验证参数
	if err := s.validateParams(params); err != nil {
		return "", err
	}

	// 序列化配置数据
	imageMaterialsJSON, _ := json.Marshal(params.ImageMaterials)
	titleSettingsJSON, _ := json.Marshal(params.TitleSettings)
	backgroundMusicsJSON, _ := json.Marshal(params.BackgroundMusics)
	stickerSettingsJSON, _ := json.Marshal(params.StickerSettings)
	categoriesJSON, _ := json.Marshal(params.CategoryIds)

	// 创建任务记录
	task := ai.AutoImageTextTask{
		TaskName:             params.TaskName,
		Status:               3, // 处理中
		CreatedBy:            userId,
		GenerateCount:        params.GenerateCount,
		Categories:           string(categoriesJSON),
		VideoTitle:           params.VideoTitle,
		ManualProductId:      params.ManualProductId,
		Topic:                params.Topic,
		ImageTextDescription: params.ImageTextDescription,
		ImageMaterials:       string(imageMaterialsJSON),
		TitleSettings:        string(titleSettingsJSON),
		BackgroundMusics:     string(backgroundMusicsJSON),
		StickerSettings:      string(stickerSettingsJSON),
		MinImageCount:        params.MinImageCount,
		MaxImageCount:        params.MaxImageCount,
		TitleApplyMode:       params.TitleApplyMode,
		StickerApplyMode:     params.StickerApplyMode,
	}

	// 获取用户昵称
	var user system.SysUser
	if err := global.GVA_DB.First(&user, userId).Error; err == nil {
		task.CreatedName = user.NickName
	}

	// 保存任务到数据库
	if err := global.GVA_DB.Create(&task).Error; err != nil {
		return "", fmt.Errorf("保存任务失败: %v", err)
	}

	// 异步处理任务
	go s.processImageTextTask(&task, params)

	return strconv.FormatUint(uint64(task.ID), 10), nil
}

// validateParams 验证请求参数
func (s *AutoImageTextService) validateParams(params request.AutoImageTextTaskParams) error {
	if params.TaskName == "" {
		return errors.New("任务名称不能为空")
	}

	if len(params.ImageMaterials) == 0 {
		return errors.New("至少需要一个图片素材")
	}

	if params.GenerateCount <= 0 {
		return errors.New("生成数量必须大于0")
	}

	if params.ImageTextDescription == "" {
		return errors.New("图文描述不能为空")
	}

	// 验证图片分组
	if err := s.validateImageGroups(params.ImageMaterials); err != nil {
		return err
	}

	// 多分组模式下，最小最大图片数量应该等于分组数量
	if len(params.ImageMaterials) > 1 {
		groupCount := len(params.ImageMaterials)
		if params.MinImageCount != groupCount || params.MaxImageCount != groupCount {
			return fmt.Errorf("多分组模式下，最小/最大图片数量必须等于分组数量(%d)", groupCount)
		}
	} else {
		// 单分组模式下，验证图片数量不能超过分组中的实际图片数
		if len(params.ImageMaterials) == 1 {
			singleGroupImageCount := s.getSingleGroupImageCount(params.ImageMaterials[0])
			if params.MinImageCount < 1 {
				return errors.New("最小图片数量不能小于1")
			}
			if params.MaxImageCount > singleGroupImageCount {
				return fmt.Errorf("最大图片数量不能超过分组中的实际图片数量(%d)", singleGroupImageCount)
			}
			if params.MinImageCount > params.MaxImageCount {
				return errors.New("最小图片数量不能大于最大图片数量")
			}
		} else {
			// 常规验证（当没有分组时的兜底）
			if params.MinImageCount < 1 || params.MaxImageCount < params.MinImageCount {
				return errors.New("图片数量设置错误")
			}
		}
	}

	return nil
}

// validateImageGroups 验证图片分组
func (s *AutoImageTextService) validateImageGroups(materials []request.ImageMaterial) error {
	for i, material := range materials {
		// 检查每个分组是否有有效的图片
		allImages := s.collectAllImages(material)
		if len(allImages) == 0 {
			return fmt.Errorf("分组%d没有有效的图片素材", i+1)
		}
	}
	return nil
}

// getSingleGroupImageCount 获取单个分组中的图片数量
func (s *AutoImageTextService) getSingleGroupImageCount(material request.ImageMaterial) int {
	count := 0

	// 计算主图片
	if material.MediaUrl != "" || material.MediaId != "" {
		count++
	}

	// 计算额外图片
	if material.ExtraMedia != nil {
		count += len(material.ExtraMedia)
	}

	return count
}

// processImageTextTask 处理图文生成任务
func (s *AutoImageTextService) processImageTextTask(task *ai.AutoImageTextTask, params request.AutoImageTextTaskParams) {
	defer func() {
		if r := recover(); r != nil {
			global.GVA_LOG.Error("图文任务处理发生panic", zap.Any("error", r))
			s.updateTaskStatus(task.ID, 2, fmt.Sprintf("任务处理异常: %v", r), 0)
		}
	}()

	successCount := 0

	// 生成指定数量的图文
	for i := 0; i < params.GenerateCount; i++ {
		if err := s.generateSingleImageText(task, params, i+1); err != nil {
			global.GVA_LOG.Error("生成图文失败", zap.Error(err), zap.Int("index", i+1))
			continue
		}
		successCount++

		// 更新进度
		progress := float64(i+1) / float64(params.GenerateCount) * 100
		s.updateTaskProgress(task.ID, progress, successCount)
	}

	// 更新最终状态
	if successCount == params.GenerateCount {
		s.updateTaskStatus(task.ID, 1, "", successCount)
	} else if successCount == 0 {
		s.updateTaskStatus(task.ID, 2, "所有图文生成失败", successCount)
	} else {
		s.updateTaskStatus(task.ID, 1, fmt.Sprintf("部分成功，成功生成%d个", successCount), successCount)
	}
}

// generateSingleImageText 生成单个图文
func (s *AutoImageTextService) generateSingleImageText(task *ai.AutoImageTextTask, params request.AutoImageTextTaskParams, index int) error {
	// 随机选择图片
	selectedImages := s.selectRandomImages(params.ImageMaterials, params.MinImageCount, params.MaxImageCount)
	if len(selectedImages) == 0 {
		return errors.New("没有可用的图片素材")
	}

	// 处理图片（添加标题和贴纸）
	processedImages, err := s.processImages(selectedImages, params.TitleSettings, params.StickerSettings, params.TitleApplyMode, params.StickerApplyMode, params.PseudoOriginalSettings)
	if err != nil {
		return fmt.Errorf("图片处理失败: %v", err)
	}

	// 序列化图片链接
	imageUrlsJSON, _ := json.Marshal(processedImages)

	// 使用图文描述作为标题
	title := params.ImageTextDescription
	if title == "" {
		// 如果没有图文描述，使用原来的逻辑生成标题
		title = s.generateTitle(params, index)
	}

	// 创建视频记录（类型为图文）
	video := creative.Video{
		CategoryId:       s.getRandomCategoryId(params.CategoryIds),
		Title:            title,
		Cover:            s.getFirstImageUrl(processedImages),
		Status:           0, // 待发布
		CreatedBy:        task.CreatedBy,
		Source:           1, // 智能生成
		MusicId:          s.getRandomMusicId(params.BackgroundMusics),
		Topic:            params.Topic,
		TaskId:           strconv.FormatUint(uint64(task.ID), 10),
		ManualProductId:  params.ManualProductId,
		ContentType:      2, // 图文
		ImageUrls:        string(imageUrlsJSON),
		MinImageCount:    params.MinImageCount,
		MaxImageCount:    params.MaxImageCount,
		TitleApplyMode:   params.TitleApplyMode,
		StickerApplyMode: params.StickerApplyMode,
	}

	// 保存到数据库
	if err := global.GVA_DB.Create(&video).Error; err != nil {
		return fmt.Errorf("保存视频记录失败: %v", err)
	}

	return nil
}

// selectRandomImages 根据分组模式随机选择图片
func (s *AutoImageTextService) selectRandomImages(materials []request.ImageMaterial, minCount, maxCount int) []string {
	if len(materials) == 0 {
		return nil
	}

	var selected []string

	// 判断是单分组还是多分组模式
	if len(materials) == 1 {
		// 单分组模式：从该分组中随机选取N张图片
		selected = s.selectFromSingleGroup(materials[0], minCount, maxCount)
	} else {
		// 多分组模式：每个分组随机选取1张图片
		selected = s.selectFromMultipleGroups(materials)
	}

	return selected
}

// selectFromSingleGroup 从单个分组中随机选择图片
func (s *AutoImageTextService) selectFromSingleGroup(material request.ImageMaterial, minCount, maxCount int) []string {
	// 收集所有可用的图片
	allImages := s.collectAllImages(material)
	if len(allImages) == 0 {
		return nil
	}

	// 随机确定图片数量
	count := minCount + rand.Intn(maxCount-minCount+1)
	if count > len(allImages) {
		count = len(allImages)
	}

	// 随机选择图片
	selected := make([]string, 0, count)
	indices := rand.Perm(len(allImages))

	for i := 0; i < count; i++ {
		selected = append(selected, allImages[indices[i]])
	}

	return selected
}

// selectFromMultipleGroups 从多个分组中各选择1张图片
func (s *AutoImageTextService) selectFromMultipleGroups(materials []request.ImageMaterial) []string {
	selected := make([]string, 0, len(materials))

	for _, material := range materials {
		// 从每个分组中随机选择1张图片
		allImages := s.collectAllImages(material)
		if len(allImages) > 0 {
			// 随机选择一张
			randomIndex := rand.Intn(len(allImages))
			selected = append(selected, allImages[randomIndex])
		}
	}

	return selected
}

// collectAllImages 收集分组中的所有图片URL
func (s *AutoImageTextService) collectAllImages(material request.ImageMaterial) []string {
	var images []string

	// 添加主素材
	if material.MediaUrl != "" {
		images = append(images, material.MediaUrl)
	} else if material.MediaId != "" {
		// 这里可以根据MediaId获取实际URL，暂时使用MediaId
		images = append(images, material.MediaId)
	}

	// 添加额外素材
	for _, extra := range material.ExtraMedia {
		if extra.Url != "" {
			images = append(images, extra.Url)
		} else if extra.MediaId != "" {
			// 这里可以根据MediaId获取实际URL，暂时使用MediaId
			images = append(images, extra.MediaId)
		}
	}

	return images
}

// processImages 处理图片（添加标题和贴纸）
func (s *AutoImageTextService) processImages(imageUrls []string, titleSettings []request.TitleSetting, stickerSettings []request.StickerSetting, titleApplyMode, stickerApplyMode int, pseudoOriginalSettings *request.PseudoOriginalSettings) ([]string, error) {
	processedUrls := make([]string, len(imageUrls))

	for i, imageUrl := range imageUrls {
		processed := imageUrl

		// 是否需要添加标题
		needTitle := titleApplyMode == 2 || (titleApplyMode == 1 && i == 0)
		// 是否需要添加贴纸
		needSticker := stickerApplyMode == 2 || (stickerApplyMode == 1 && i == 0)

		if needTitle || needSticker {
			// 这里应该调用FFmpeg处理图片
			// 暂时返回原URL，实际需要实现FFmpeg处理逻辑
			processed = s.processImageWithFFmpeg(imageUrl, titleSettings, stickerSettings, needTitle, needSticker)
		}

		// 为所有图片进行伪原创处理
		if processed != "" {
			pseudoOriginalUrl, err := s.applyPseudoOriginalProcessing(processed, pseudoOriginalSettings)
			if err != nil {
				global.GVA_LOG.Error("伪原创处理失败", zap.Error(err), zap.String("imageUrl", processed))
				// 如果伪原创处理失败，继续使用原图
			} else {
				processed = pseudoOriginalUrl
			}
		}

		processedUrls[i] = processed
	}

	return processedUrls, nil
}

// processImageWithFFmpeg 使用FFmpeg处理图片
func (s *AutoImageTextService) processImageWithFFmpeg(imageUrl string, titleSettings []request.TitleSetting, stickerSettings []request.StickerSetting, needTitle, needSticker bool) string {
	// 如果不需要添加标题和贴纸，直接返回原URL
	if !needTitle && !needSticker {
		return imageUrl
	}

	// 下载原图片
	localImagePath, err := s.downloadImage(imageUrl)
	if err != nil {
		global.GVA_LOG.Error("下载图片失败", zap.Error(err), zap.String("imageUrl", imageUrl))
		return imageUrl // 失败时返回原URL
	}
	// 获取临时目录路径
	tempDir := filepath.Dir(localImagePath)
	defer os.RemoveAll(tempDir) // 清理整个临时目录

	// 处理图片
	processedImagePath, err := s.processImageWithEffects(localImagePath, titleSettings, stickerSettings, needTitle, needSticker)
	if err != nil {
		global.GVA_LOG.Error("图片处理失败", zap.Error(err), zap.String("localImagePath", localImagePath))
		return imageUrl // 失败时返回原URL
	}

	// 上传处理后的图片
	newImageUrl, err := s.uploadProcessedImage(processedImagePath)
	if err != nil {
		global.GVA_LOG.Error("上传处理后的图片失败", zap.Error(err), zap.String("processedImagePath", processedImagePath))
		return imageUrl // 失败时返回原URL
	}

	global.GVA_LOG.Info("图片处理成功", zap.String("originalUrl", imageUrl), zap.String("processedUrl", newImageUrl))
	return newImageUrl
}

// generateTitle 生成标题
func (s *AutoImageTextService) generateTitle(params request.AutoImageTextTaskParams, index int) string {
	if params.VideoTitle != "" {
		return fmt.Sprintf("%s_%d", params.VideoTitle, index)
	}

	if len(params.TitleSettings) > 0 {
		// 随机选择一个标题
		titleSetting := params.TitleSettings[rand.Intn(len(params.TitleSettings))]
		return fmt.Sprintf("%s_%d", titleSetting.Content, index)
	}

	return fmt.Sprintf("图文作品_%d_%s", index, time.Now().Format("20060102150405"))
}

// getRandomCategoryId 获取随机分类ID
func (s *AutoImageTextService) getRandomCategoryId(categoryIds []uint) uint {
	if len(categoryIds) == 0 {
		return 0
	}
	return categoryIds[rand.Intn(len(categoryIds))]
}

// getRandomMusicId 获取随机音乐ID
func (s *AutoImageTextService) getRandomMusicId(backgroundMusics []request.BackgroundMusic) string {
	if len(backgroundMusics) == 0 {
		return ""
	}
	music := backgroundMusics[rand.Intn(len(backgroundMusics))]
	return music.MediaId
}

// getFirstImageUrl 获取第一张图片URL作为封面
func (s *AutoImageTextService) getFirstImageUrl(imageUrls []string) string {
	if len(imageUrls) == 0 {
		return ""
	}
	return imageUrls[0]
}

// updateTaskStatus 更新任务状态
func (s *AutoImageTextService) updateTaskStatus(taskId uint, status int, errorMsg string, successCount int) {
	updates := map[string]interface{}{
		"status":        status,
		"error_msg":     errorMsg,
		"success_count": successCount,
	}
	global.GVA_DB.Model(&ai.AutoImageTextTask{}).Where("id = ?", taskId).Updates(updates)
}

// updateTaskProgress 更新任务进度
func (s *AutoImageTextService) updateTaskProgress(taskId uint, progress float64, successCount int) {
	updates := map[string]interface{}{
		"success_count": successCount,
	}
	global.GVA_DB.Model(&ai.AutoImageTextTask{}).Where("id = ?", taskId).Updates(updates)
}

// GetAutoImageTextTaskStatus 获取任务状态
func (s *AutoImageTextService) GetAutoImageTextTaskStatus(taskId string) (*ai.AutoImageTextTaskStatus, error) {
	id, err := strconv.ParseUint(taskId, 10, 32)
	if err != nil {
		return nil, errors.New("无效的任务ID")
	}

	var task ai.AutoImageTextTask
	if err := global.GVA_DB.First(&task, uint(id)).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("任务不存在")
		}
		return nil, err
	}

	// 计算进度
	progress := float64(0)
	if task.GenerateCount > 0 {
		progress = float64(task.SuccessCount) / float64(task.GenerateCount) * 100
	}

	status := &ai.AutoImageTextTaskStatus{
		Status:       task.Status,
		Progress:     progress,
		ErrorMsg:     task.ErrorMsg,
		SuccessCount: task.SuccessCount,
		TotalCount:   task.GenerateCount,
	}

	return status, nil
}

// ListAutoImageTextTasks 获取任务列表
func (s *AutoImageTextService) ListAutoImageTextTasks(params request.AutoImageTextTaskListParams) ([]ai.AutoImageTextTask, int64, error) {
	db := global.GVA_DB.Model(&ai.AutoImageTextTask{})

	// 添加查询条件
	if params.TaskName != "" {
		db = db.Where("task_name LIKE ?", "%"+params.TaskName+"%")
	}
	if params.Status != nil {
		db = db.Where("status = ?", *params.Status)
	}
	if params.CreatedBy != nil {
		db = db.Where("created_by = ?", *params.CreatedBy)
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	var tasks []ai.AutoImageTextTask
	offset := (params.Page - 1) * params.PageSize
	if err := db.Offset(offset).Limit(params.PageSize).Order("created_at DESC").Find(&tasks).Error; err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// DeleteAutoImageTextTask 删除任务
func (s *AutoImageTextService) DeleteAutoImageTextTask(task ai.AutoImageTextTask) error {
	return global.GVA_DB.Delete(&task).Error
}

// downloadImage 下载图片到本地临时文件
func (s *AutoImageTextService) downloadImage(imageUrl string) (string, error) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "imagetext-process")
	if err != nil {
		return "", fmt.Errorf("创建临时目录失败: %w", err)
	}

	// 下载图片
	resp, err := http.Get(imageUrl)
	if err != nil {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	// 生成临时文件名
	fileName := fmt.Sprintf("original_%d.jpg", time.Now().UnixNano())
	localPath := filepath.Join(tempDir, fileName)

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("创建本地文件失败: %w", err)
	}
	defer file.Close()

	// 保存图片内容
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("保存图片失败: %w", err)
	}

	return localPath, nil
}

// processImageWithEffects 使用FFmpeg为图片添加标题和贴纸
func (s *AutoImageTextService) processImageWithEffects(
	imagePath string,
	titleSettings []request.TitleSetting,
	stickerSettings []request.StickerSetting,
	needTitle,
	needSticker bool,
) (string, error) {
	// 生成输出文件路径
	outputPath := strings.Replace(imagePath, "original_", "processed_", 1)

	// 如果不需要添加任何效果，直接复制文件
	if !needTitle && !needSticker {
		return s.copyFile(imagePath, outputPath)
	}

	// 构建FFmpeg命令参数
	var args []string
	var filterComplexParts []string
	var inputCount int

	// 主图片输入
	args = append(args, "-i", imagePath)
	inputCount++

	// 下载并添加贴纸文件作为输入
	var stickerPaths []string
	if needSticker && len(stickerSettings) > 0 {
		for _, sticker := range stickerSettings {
			if sticker.MediaUrl == "" && sticker.MediaId == "" {
				continue
			}

			// 获取贴纸URL
			stickerUrl := sticker.MediaUrl
			if stickerUrl == "" {
				global.GVA_LOG.Warn("贴纸没有有效的URL，跳过", zap.String("mediaId", sticker.MediaId))
				continue
			}

			stickerPath, err := s.downloadImage(stickerUrl)
			if err != nil {
				global.GVA_LOG.Error("下载贴纸失败", zap.Error(err), zap.String("stickerUrl", stickerUrl))
				continue
			}

			stickerPaths = append(stickerPaths, stickerPath)
			args = append(args, "-i", stickerPath)
			inputCount++
		}
	}

	// 构建滤镜链
	var currentInput string = "[0:v]"

	// 处理贴纸叠加
	if needSticker && len(stickerPaths) > 0 {
		stickerIndex := 0
		for i, sticker := range stickerSettings {
			if sticker.MediaUrl == "" && sticker.MediaId == "" {
				continue
			}

			// 检查贴纸URL是否可用（前面已经下载过的）
			if sticker.MediaUrl == "" {
				continue
			}

			if stickerIndex >= len(stickerPaths) {
				break
			}

			// 构建贴纸overlay滤镜
			stickerInputIndex := 1 + stickerIndex // 主图片是0，贴纸从1开始

			// 计算贴纸的位置和大小
			var overlayFilter string

			// 处理贴纸缩放
			scale := sticker.Scale
			if scale <= 0 {
				scale = 1.0 // 默认不缩放
			}

			// 计算X和Y坐标，基于中心点定位
			var xPos, yPos string
			// 检查是否设置了有效的坐标值（0-1范围内的浮点数）
			if sticker.X >= 0 && sticker.X <= 1 && sticker.Y >= 0 && sticker.Y <= 1 {
				// 使用具体的相对坐标（百分比），基于贴纸中心点定位
				// 坐标表示贴纸中心点的位置，需要减去贴纸尺寸的一半
				xPos = fmt.Sprintf("main_w*%.3f-overlay_w/2", sticker.X)
				yPos = fmt.Sprintf("main_h*%.3f-overlay_h/2", sticker.Y)
			} else {
				// 使用预设位置，也基于中心点
				switch sticker.Position {
				case "top-left":
					xPos = "overlay_w/2+20"
					yPos = "overlay_h/2+20"
				case "top-right":
					xPos = "main_w-overlay_w/2-20"
					yPos = "overlay_h/2+20"
				case "bottom-left":
					xPos = "overlay_w/2+20"
					yPos = "main_h-overlay_h/2-20"
				case "bottom-right":
					xPos = "main_w-overlay_w/2-20"
					yPos = "main_h-overlay_h/2-20"
				case "center":
					xPos = "main_w/2"
					yPos = "main_h/2"
				default: // 默认居中
					xPos = "main_w/2"
					yPos = "main_h/2"
				}
			}

			// 生成当前步骤的输出标签
			outputLabel := fmt.Sprintf("[overlay%d]", i)

			// 处理旋转和缩放
			if sticker.Rotation != 0 || scale != 1.0 {
				// 需要先对贴纸进行变换
				var transforms []string

				// 缩放
				if scale != 1.0 {
					transforms = append(transforms, fmt.Sprintf("scale=iw*%.3f:ih*%.3f", scale, scale))
				}

				// 旋转
				if sticker.Rotation != 0 {
					// 将角度转换为弧度
					rotationRadians := sticker.Rotation * 3.14159 / 180
					transforms = append(transforms, fmt.Sprintf("rotate=%.6f", rotationRadians))
				}

				if len(transforms) > 0 {
					transformFilter := strings.Join(transforms, ",")
					stickerLabel := fmt.Sprintf("[sticker%d]", i)
					stickerPreFilter := fmt.Sprintf("[%d:v]%s%s", stickerInputIndex, transformFilter, stickerLabel)
					filterComplexParts = append(filterComplexParts, stickerPreFilter)

					// overlay滤镜使用变换后的贴纸
					overlayFilter = fmt.Sprintf("%s%soverlay=%s:%s%s", currentInput, stickerLabel, xPos, yPos, outputLabel)
				}
			} else {
				// 直接overlay，不需要预处理
				overlayFilter = fmt.Sprintf("%s[%d:v]overlay=%s:%s%s", currentInput, stickerInputIndex, xPos, yPos, outputLabel)
			}

			filterComplexParts = append(filterComplexParts, overlayFilter)
			currentInput = outputLabel // 下一个操作的输入是当前操作的输出
			stickerIndex++
		}
	}

	// 添加文字滤镜
	if needTitle && len(titleSettings) > 0 {
		for _, title := range titleSettings {
			if title.Content == "" {
				continue
			}

			// 计算文字位置 - 根据height字段计算Y位置
			var yPos string
			if title.Height > 0 {
				// height是相对比例，转换为像素位置
				yPosPercent := title.Height * 100
				yPos = fmt.Sprintf("h*%.2f/100", yPosPercent)
			} else if title.Position != "" {
				// 兼容旧的position字段
				switch title.Position {
				case "top":
					yPos = "50"
				case "bottom":
					yPos = "h-text_h-50"
				default: // middle
					yPos = "(h-text_h)/2"
				}
			} else {
				// 默认居中
				yPos = "(h-text_h)/2"
			}

			// 文字对齐
			var xPos string
			switch title.Alignment {
			case "left":
				xPos = "50"
			case "right":
				xPos = "w-text_w-50"
			default: // center
				xPos = "(w-text_w)/2"
			}

			// 构建文字滤镜 - 使用相对于图片高度的字体大小
			baseFontSize := title.FontSize
			if baseFontSize <= 0 {
				baseFontSize = 24
			}

			// 将字体大小转换为相对于图片高度的比例
			fontSizeRatio := float64(baseFontSize) / 1000.0 // 基准高度1000px，字体更小
			fontSizeExpression := fmt.Sprintf("h*%.4f", fontSizeRatio)

			// 优先使用FontStyle.Color，其次使用FontColor兼容字段
			fontColor := title.FontStyle.Color
			if fontColor == "" && title.FontColor != "" {
				fontColor = title.FontColor
			}
			if fontColor == "" {
				fontColor = "white"
			}

			// 转义特殊字符
			content := strings.ReplaceAll(title.Content, "'", "\\'")
			content = strings.ReplaceAll(content, ":", "\\:")

			textFilter := fmt.Sprintf("%sdrawtext=text='%s':fontsize=%s:fontcolor=%s:x=%s:y=%s",
				currentInput, content, fontSizeExpression, fontColor, xPos, yPos)

			// 如果指定了字体
			if title.FontFamily != "" {
				fontPath := s.getFontPath(title.FontFamily)
				if fontPath != "" {
					textFilter += ":fontfile=" + fontPath
				}
			}

			// 处理字体样式
			if title.FontStyle.StyleType == "background" && title.FontStyle.BackgroundColor != "" {
				// 添加背景色 - 使用box参数
				textFilter += fmt.Sprintf(":box=1:boxcolor=%s@0.8:boxborderw=5", title.FontStyle.BackgroundColor)
			} else if title.FontStyle.StyleType == "border" && title.FontStyle.BorderColor != "" {
				// 添加描边效果
				borderWidth := title.FontStyle.BorderWidth
				if borderWidth <= 0 {
					borderWidth = 2
				}
				textFilter += fmt.Sprintf(":borderw=%d:bordercolor=%s", borderWidth, title.FontStyle.BorderColor)
			}

			// 生成输出标签
			outputLabel := fmt.Sprintf("[text%d]", len(filterComplexParts))
			textFilter += outputLabel

			filterComplexParts = append(filterComplexParts, textFilter)
			currentInput = outputLabel // 下一个操作的输入是当前操作的输出
		}
	}

	// 构建完整的FFmpeg命令
	if len(filterComplexParts) > 0 {
		// 使用filter_complex
		filterComplex := strings.Join(filterComplexParts, ";")

		// 如果最终输出有标签，需要用-map映射输出
		if strings.Contains(currentInput, "[") && strings.Contains(currentInput, "]") {
			args = append(args, "-filter_complex", filterComplex)
			args = append(args, "-map", currentInput) // 保留完整的标签，如 [overlay0]
		} else {
			// 如果没有输出标签，FFmpeg会自动使用最后一个滤镜的输出
			args = append(args, "-filter_complex", filterComplex)
		}
	} else {
		// 如果没有滤镜，直接复制
		return s.copyFile(imagePath, outputPath)
	}

	// 输出设置
	args = append(args, "-y", outputPath)

	// 执行FFmpeg命令
	cmd := exec.Command("ffmpeg", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		global.GVA_LOG.Error("FFmpeg处理失败",
			zap.String("cmd", cmd.String()),
			zap.String("output", string(output)),
			zap.Error(err))
		return "", fmt.Errorf("FFmpeg处理失败: %w", err)
	}

	// 检查输出文件是否生成
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		return "", fmt.Errorf("处理后的图片文件未生成")
	}

	// 清理临时贴纸文件
	for _, stickerPath := range stickerPaths {
		stickerDir := filepath.Dir(stickerPath)
		os.RemoveAll(stickerDir)
	}

	global.GVA_LOG.Info("图片处理完成", zap.String("outputPath", outputPath))
	return outputPath, nil
}

// uploadProcessedImage 上传处理后的图片
func (s *AutoImageTextService) uploadProcessedImage(filePath string) (string, error) {
	// 使用项目的上传工具
	uploadedUrl, _, err := upload.UploadFileFromPath(filePath)
	if err != nil {
		return "", fmt.Errorf("上传文件失败: %w", err)
	}

	return uploadedUrl, nil
}

// copyFile 复制文件（当不需要处理时使用）
func (s *AutoImageTextService) copyFile(src, dst string) (string, error) {
	srcFile, err := os.Open(src)
	if err != nil {
		return "", err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return "", err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return "", err
	}

	return dst, nil
}

// getFontPath 获取字体文件路径 - 主要从OSS下载字体文件
func (s *AutoImageTextService) getFontPath(fontFamily string) string {
	if fontFamily == "" {
		return ""
	}

	// 如果传入的是字体文件名（不包含路径分隔符），则从 OSS 获取
	if !strings.Contains(fontFamily, "/") && strings.HasSuffix(fontFamily, ".ttf") {
		return s.downloadOSSFont(fontFamily)
	}

	// 如果传入的是完整路径，去掉前缀获取文件名
	if strings.HasPrefix(fontFamily, "/fonts/") {
		fontFileName := strings.TrimPrefix(fontFamily, "/fonts/")
		return s.downloadOSSFont(fontFileName)
	}

	// 对于其他格式的字体名称，也尝试从OSS下载
	// 如果不是.ttf文件，添加.ttf后缀再尝试
	fontFileName := fontFamily
	if !strings.HasSuffix(fontFileName, ".ttf") {
		fontFileName += ".ttf"
	}

	// 尝试从OSS下载，如果失败则让FFmpeg使用系统默认字体
	ossPath := s.downloadOSSFont(fontFileName)
	if ossPath != "" {
		return ossPath
	}

	// 如果OSS下载失败，记录警告并返回空字符串，让FFmpeg使用系统默认字体
	global.GVA_LOG.Warn("字体文件下载失败，将使用系统默认字体", zap.String("fontFamily", fontFamily))
	return ""
}

// downloadOSSFont 从 OSS 下载字体文件到本地临时目录
func (s *AutoImageTextService) downloadOSSFont(fontFileName string) string {
	// OSS 字体基础 URL
	ossBaseURL := "https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font/"
	fontURL := ossBaseURL + fontFileName

	// 创建字体缓存目录
	fontCacheDir := filepath.Join(os.TempDir(), "font_cache")
	if err := os.MkdirAll(fontCacheDir, 0755); err != nil {
		global.GVA_LOG.Error("创建字体缓存目录失败", zap.Error(err))
		return ""
	}

	// 本地字体文件路径
	localFontPath := filepath.Join(fontCacheDir, fontFileName)

	// 如果本地已存在该字体文件，直接返回
	if _, err := os.Stat(localFontPath); err == nil {
		global.GVA_LOG.Debug("使用缓存的字体文件", zap.String("fontPath", localFontPath))
		return localFontPath
	}

	// 下载字体文件
	resp, err := http.Get(fontURL)
	if err != nil {
		global.GVA_LOG.Error("下载字体文件失败", zap.Error(err), zap.String("fontURL", fontURL))
		return ""
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("字体文件下载失败", zap.Int("statusCode", resp.StatusCode), zap.String("fontURL", fontURL))
		return ""
	}

	// 创建本地字体文件
	fontFile, err := os.Create(localFontPath)
	if err != nil {
		global.GVA_LOG.Error("创建本地字体文件失败", zap.Error(err), zap.String("localFontPath", localFontPath))
		return ""
	}
	defer fontFile.Close()

	// 保存字体文件内容
	_, err = io.Copy(fontFile, resp.Body)
	if err != nil {
		global.GVA_LOG.Error("保存字体文件失败", zap.Error(err), zap.String("localFontPath", localFontPath))
		os.Remove(localFontPath) // 清理失败的文件
		return ""
	}

	global.GVA_LOG.Info("字体文件下载成功", zap.String("fontFileName", fontFileName), zap.String("localPath", localFontPath))
	return localFontPath
}

// applyPseudoOriginalProcessing 对图片进行伪原创处理
func (s *AutoImageTextService) applyPseudoOriginalProcessing(imageUrl string, pseudoOriginalSettings *request.PseudoOriginalSettings) (string, error) {
	// 下载图片
	localImagePath, err := s.downloadImage(imageUrl)
	if err != nil {
		return "", fmt.Errorf("下载图片失败: %w", err)
	}

	// 获取临时目录路径
	tempDir := filepath.Dir(localImagePath)
	defer os.RemoveAll(tempDir) // 清理临时目录

	// 加载图片
	img, err := imaging.Open(localImagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %w", err)
	}

	// 应用伪原创处理
	processedImg, err := s.applyRandomImageTransforms(img, pseudoOriginalSettings)
	if err != nil {
		return "", fmt.Errorf("图片变换失败: %w", err)
	}

	// 保存处理后的图片
	outputPath := strings.Replace(localImagePath, "original_", "pseudo_original_", 1)
	err = s.saveImageWithRandomQuality(processedImg, outputPath)
	if err != nil {
		return "", fmt.Errorf("保存图片失败: %w", err)
	}

	// 上传处理后的图片
	newImageUrl, err := s.uploadProcessedImage(outputPath)
	if err != nil {
		return "", fmt.Errorf("上传图片失败: %w", err)
	}

	global.GVA_LOG.Info("伪原创处理成功", zap.String("originalUrl", imageUrl), zap.String("processedUrl", newImageUrl))
	return newImageUrl, nil
}

// applyRandomImageTransforms 应用随机图像变换
func (s *AutoImageTextService) applyRandomImageTransforms(img image.Image, pseudoOriginalSettings *request.PseudoOriginalSettings) (image.Image, error) {
	// 获取原图尺寸
	bounds := img.Bounds()
	width := bounds.Max.X
	height := bounds.Max.Y

	// 设置默认值
	var brightnessMin, brightnessMax float64 = 0.9, 1.1
	var contrastMin, contrastMax float64 = 0.95, 1.05
	var saturationMin, saturationMax float64 = 0.95, 1.05
	var rotationMin, rotationMax float64 = -0.1, 0.1
	var sizeMin, sizeMax float64 = 0.98, 1.02

	// 如果有配置参数，直接使用前端传过来的值
	if pseudoOriginalSettings != nil {
		brightnessMin, brightnessMax = pseudoOriginalSettings.BrightnessMin, pseudoOriginalSettings.BrightnessMax
		contrastMin, contrastMax = pseudoOriginalSettings.ContrastMin, pseudoOriginalSettings.ContrastMax
		saturationMin, saturationMax = pseudoOriginalSettings.SaturationMin, pseudoOriginalSettings.SaturationMax
		rotationMin, rotationMax = pseudoOriginalSettings.RotationMin, pseudoOriginalSettings.RotationMax
		sizeMin, sizeMax = pseudoOriginalSettings.SizeMin, pseudoOriginalSettings.SizeMax
	}

	// 1. 随机调整亮度
	brightnessRange := brightnessMax - brightnessMin
	brightnessMultiplier := brightnessMin + rand.Float64()*brightnessRange
	// 转换倍数为百分比：percentage = (multiplier - 1) * 100
	brightnessPercentage := (brightnessMultiplier - 1) * 100
	global.GVA_LOG.Info("应用亮度调整", zap.Float64("brightnessMultiplier", brightnessMultiplier), zap.Float64("brightnessPercentage", brightnessPercentage))
	img = imaging.AdjustBrightness(img, brightnessPercentage)

	// 2. 随机调整对比度
	contrastRange := contrastMax - contrastMin
	contrastMultiplier := contrastMin + rand.Float64()*contrastRange
	// 转换倍数为百分比：percentage = (multiplier - 1) * 100
	contrastPercentage := (contrastMultiplier - 1) * 100
	global.GVA_LOG.Info("应用对比度调整", zap.Float64("contrastMultiplier", contrastMultiplier), zap.Float64("contrastPercentage", contrastPercentage))
	img = imaging.AdjustContrast(img, contrastPercentage)

	// 3. 随机调整饱和度
	saturationRange := saturationMax - saturationMin
	saturationMultiplier := saturationMin + rand.Float64()*saturationRange
	// 转换倍数为百分比：percentage = (multiplier - 1) * 100
	saturationPercentage := (saturationMultiplier - 1) * 100
	global.GVA_LOG.Info("应用饱和度调整", zap.Float64("saturationMultiplier", saturationMultiplier), zap.Float64("saturationPercentage", saturationPercentage))
	img = imaging.AdjustSaturation(img, saturationPercentage)

	// 4. 随机轻微旋转
	rotationRange := rotationMax - rotationMin
	rotationAngle := rotationMin + rand.Float64()*rotationRange
	global.GVA_LOG.Info("应用旋转调整", zap.Float64("rotationAngle", rotationAngle))
	img = imaging.Rotate(img, rotationAngle, color.RGBA{255, 255, 255, 0})

	// 5. 随机轻微尺寸调整
	sizeRange := sizeMax - sizeMin
	sizeAdjust := sizeMin + rand.Float64()*sizeRange
	newWidth := int(float64(width) * sizeAdjust)
	newHeight := int(float64(height) * sizeAdjust)
	img = imaging.Resize(img, newWidth, newHeight, imaging.Lanczos)

	// 6. 添加轻微的噪点
	img = s.addSubtleNoise(img)

	return img, nil
}

// addSubtleNoise 添加轻微的噪点
func (s *AutoImageTextService) addSubtleNoise(img image.Image) image.Image {
	bounds := img.Bounds()
	width := bounds.Max.X
	height := bounds.Max.Y

	// 创建一个新的RGBA图像
	noiseImg := image.NewRGBA(bounds)

	// 复制原图像到新图像
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			originalPixel := color.RGBAModel.Convert(img.At(x, y)).(color.RGBA)
			noiseImg.Set(x, y, originalPixel)
		}
	}

	// 添加轻微的随机噪点 (只影响1%的像素)
	noiseIntensity := 5                              // 噪点强度
	noisePixels := int(float64(width*height) * 0.01) // 1%的像素

	for i := 0; i < noisePixels; i++ {
		x := rand.Intn(width)
		y := rand.Intn(height)

		originalPixel := color.RGBAModel.Convert(img.At(x, y)).(color.RGBA)

		// 添加随机噪点
		noiseR := int(originalPixel.R) + rand.Intn(noiseIntensity*2) - noiseIntensity
		noiseG := int(originalPixel.G) + rand.Intn(noiseIntensity*2) - noiseIntensity
		noiseB := int(originalPixel.B) + rand.Intn(noiseIntensity*2) - noiseIntensity

		// 限制在0-255范围内
		noiseR = int(math.Max(0, math.Min(255, float64(noiseR))))
		noiseG = int(math.Max(0, math.Min(255, float64(noiseG))))
		noiseB = int(math.Max(0, math.Min(255, float64(noiseB))))

		noiseImg.Set(x, y, color.RGBA{
			R: uint8(noiseR),
			G: uint8(noiseG),
			B: uint8(noiseB),
			A: originalPixel.A,
		})
	}

	return noiseImg
}

// saveImageWithRandomQuality 以随机质量保存图片
func (s *AutoImageTextService) saveImageWithRandomQuality(img image.Image, outputPath string) error {
	// 创建输出文件
	outFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(outputPath))

	switch ext {
	case ".jpg", ".jpeg":
		// JPEG格式，随机质量 (85-95)
		quality := 85 + rand.Intn(11)
		err = jpeg.Encode(outFile, img, &jpeg.Options{Quality: quality})
	case ".png":
		// PNG格式
		err = png.Encode(outFile, img)
	default:
		// 默认使用JPEG格式
		quality := 85 + rand.Intn(11)
		err = jpeg.Encode(outFile, img, &jpeg.Options{Quality: quality})
	}

	return err
}
