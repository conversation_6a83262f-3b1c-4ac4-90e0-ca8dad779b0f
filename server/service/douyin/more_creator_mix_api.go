package douyin

import (
	"encoding/json"
	"fmt"
	"sync"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

type MoreCreatorMixApiService struct {
	client *DyHttpClient
	once   sync.Once
}

// 创作者缝合版
var MoreCreatorMixApiApp = new(MoreCreatorMixApiService)

func (s *MoreCreatorMixApiService) GetClient() *DyHttpClient {
	if s.client == nil {
		s.once.Do(func() {
			s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.MixUrl)
		})
	}

	return s.client
}

// 解析商品链接
func (s *MoreCreatorMixApiService) ParseGoodsLink(req request.MoreCreatorMixApiGoodsParseRequest) (resp response.MoreCreatorMixApiGoodsParseResponse, err error) {
	formData := map[string]string{
		"cookie":         req.Cookie, // web端的cookie
		"promotion_link": req.PromotionLink,
	}

	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}

	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/goods_link", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求获取商品链接报错：code: %d,message:%s,msg:%s", resp.Code, resp.Message, resp.Msg)
	}
	if resp.Data.Code != "" {
		return resp, fmt.Errorf("获取商品链接报错：code: %s,message:%s", resp.Data.Code, resp.Data.Msg)
	}

	return resp, nil
}

// 更新购物车挂载
func (s *MoreCreatorMixApiService) UpdatePromotion(req request.MoreCreatorMixApiUpdatePromotionRequest) (resp response.MoreCreatorMixApiUpdatePromotionResponse, err error) {
	formData := map[string]string{
		"cookie":        req.Cookie, // web端的cookie
		"promotion_id":  req.PromotionId,
		"elastic_title": req.ElasticTitle,
		"elastic_img":   req.ElasticImg,
		"product_id":    req.ProductId,
	}

	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}

	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/update_promotion", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求获取商品挂载id报错：code: %d,message:%s", resp.Code, resp.Message)
	}
	if resp.Data.Code != "" {
		return resp, fmt.Errorf("获取商品链接id报错：code: %s,message:%s", resp.Data.Code, resp.Data.Msg)
	}
	if resp.Data.StatusCode != 0 {
		return resp, fmt.Errorf("获取商品链接id失败：status_code: %d,status_msg:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
	}

	return resp, nil
}

// 挂载多个购物车
func (s *MoreCreatorMixApiService) MountMutliPromotions(req request.MoreCreatorMixApiMountPromotionRequest) (resp response.MoreCreatorMixApiUpdatePromotionResponse, err error) {
	fmt.Printf("多链接挂载id请求参数: %+v\n", req.RawDraft)

	formData := map[string]string{
		"cookie": req.Cookie, // web端的cookie
		"proxy":  utils.HttpProxy(req.Proxy),
	}
	rawDraftByte, err := json.Marshal(req.RawDraft)
	if err != nil {
		return resp, fmt.Errorf("解析raw_draft失败：%v", err)
	}
	formData["raw_draft"] = string(rawDraftByte)

	if req.DraftId != "" {
		formData["draft_id"] = req.DraftId
	}

	fmt.Printf("获取多链接挂载id请求参数: %+v\n", formData)
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/update_promotion", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求获取商品挂载id报错：code: %d,message:%s", resp.Code, resp.Message)
	}
	if resp.Data.Code != "" {
		return resp, fmt.Errorf("获取商品链接id报错：code: %s,message:%s", resp.Data.Code, resp.Data.Msg)
	}

	return resp, nil
}

// 发布推广视频
func (s *MoreCreatorMixApiService) CreatePromotionVod(req request.MoreCreatorMixApiCreateVodRequest) (resp response.MoreCreatorCustomApiMixCreateVodResponse, err error) {
	formData := map[string]string{}
	formData["cookie"] = req.Cookie
	formData["description"] = req.Description
	formData["visibility_type"] = req.VisibilityType
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.VideoVid != "" {
		formData["video_vid"] = req.VideoVid
	}
	if req.Download != "" {
		formData["download"] = req.Download
	}
	if req.Challenges != "" {
		formData["challenges"] = req.Challenges
	}
	if req.Timing != "" {
		formData["timing"] = req.Timing
	}
	if req.PoiName != "" {
		formData["poi_name"] = req.PoiName
	}
	if req.PoiId != "" {
		formData["poi_id"] = req.PoiId
	}
	if req.ShopDraftId != "" {
		formData["shop_draft_id"] = req.ShopDraftId
	}
	if req.MusicId != "" {
		formData["music_id"] = req.MusicId
	}

	remoteFiles := map[string]string{}
	if req.UploadPosterPath != "" {
		remoteFiles["upload_poster"] = req.UploadPosterPath
	}
	if req.VideoPath != "" {
		remoteFiles["video"] = req.VideoPath
	}

	fmt.Printf("发布视频请求参数: %+v\n", formData)
	err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/create_vod", formData, nil, remoteFiles, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Status != 0 {
		return resp, fmt.Errorf("发布抖音视频失败：服务器报错code: %d,message:%s", resp.Status, resp.Message)
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("发布抖音视频失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// PublishVideo 发布视频
func (s *MoreCreatorMixApiService) CustomCreateVod(req request.MoreCreatorCustomApiMixCreateVodRequest) (resp response.MoreCreatorCustomApiMixCreateVodResponse, err error) {
	formData := map[string]string{}
	formData["cookie"] = req.Cookie
	formData["description"] = req.Description
	formData["visibility_type"] = req.VisibilityType
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.VideoVid != "" {
		formData["video_vid"] = req.VideoVid
	}
	if req.Download != "" {
		formData["download"] = req.Download
	}
	if req.Challenges != "" {
		formData["challenges"] = req.Challenges
	}
	if req.Timing != "" {
		formData["timing"] = req.Timing
	}
	if req.PoiName != "" {
		formData["poi_name"] = req.PoiName
	}
	if req.PoiId != "" {
		formData["poi_id"] = req.PoiId
	}
	if req.MusicId != "" {
		formData["music_id"] = req.MusicId
	}

	remoteFiles := map[string]string{}
	if req.UploadPosterPath != "" {
		remoteFiles["upload_poster"] = req.UploadPosterPath
	}
	if req.VideoPath != "" {
		remoteFiles["video"] = req.VideoPath
	}

	err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/create_vod", formData, nil, remoteFiles, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Status != 0 {
		return resp, fmt.Errorf("发布抖音视频失败：服务器报错code: %d,message:%s", resp.Status, resp.Message)
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("发布抖音视频失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// 发布图文
func (s *MoreCreatorMixApiService) CreateNote(req request.MoreCreatorMixApiCreateNoteRequest) (resp response.MoreCreatorMixApiGoodsParseResponse, err error) {
	formData := map[string]string{
		"cookie":          req.Cookie, // web端的cookie
		"proxy":           utils.HttpProxy(req.Proxy),
		"visibility_type": req.VisibilityType,
		"description":     req.Description,
	}

	// 上传封面
	remoteUrlMap := map[string]string{
		"upload_poster": req.UploadPoster,
	}

	err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/create_note", formData, nil, remoteUrlMap, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求发布图文失败：code: %d,message:%s,msg:%s", resp.Code, resp.Message, resp.Msg)
	}
	if resp.Data.Code != "" {
		return resp, fmt.Errorf("发布图文失败：code: %s,message:%s", resp.Data.Code, resp.Data.Msg)
	}
	if resp.Data.StatusCode != 0 {
		return resp, fmt.Errorf("获发布图文失败：status_code: %d,status_msg:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
	}

	return resp, nil
}

// 待废弃，移到logic中执行
// func (s *MoreCreatorMixApiService) PublishPromotionVod(req request.MoreCreatorMixApiCreatePromotionRequest) (resp response.MoreCreatorCustomApiCustomCreateVodResponse, err error) {
// 	parseReq := request.MoreCreatorMixApiGoodsParseRequest{
// 		Cookie:        req.WebCookie,
// 		PromotionLink: req.PromotionLink,
// 		Proxy:         req.Proxy,
// 	}
// 	parseResp, err := s.ParseGoodsLink(parseReq)
// 	if err != nil {
// 		fmt.Printf("解析商品链接报错:%v", err)
// 		return resp, err
// 	}
// 	// 解析商品链接
// 	imgStr := ""
// 	for _, img := range parseResp.Data.Promotions[0].Imgs {
// 		if imgStr != "" {
// 			imgStr += img.Uri
// 		} else {
// 			imgStr = fmt.Sprintf("%s,%s", imgStr, img.Uri)
// 		}
// 	}
// 	// 更新购物车挂载
// 	updateReq := request.MoreCreatorMixApiUpdatePromotionRequest{
// 		Cookie:       req.WebCookie,
// 		Proxy:        req.Proxy,
// 		PromotionId:  parseResp.Data.Promotions[0].PromotionId,
// 		ElasticTitle: req.PromotionTitle,
// 		ElasticImg:   imgStr,
// 		ProductId:    parseResp.Data.Promotions[0].Gid,
// 	}
// 	updateResp, err := s.UpdatePromotion(updateReq)
// 	if err != nil {
// 		return resp, err
// 	}
// 	// 发布视频
// 	createVodReq := request.MoreCreatorMixApiCreateVodRequest{
// 		Cookie:           req.Cookie,
// 		Proxy:            req.Proxy,
// 		Description:      req.Description,
// 		UploadPosterPath: req.UploadPosterPath,
// 		VisibilityType:   req.VisibilityType,
// 		VideoPath:        req.VideoPath,
// 		VideoVid:         req.VideoVid,
// 		Download:         req.Download,
// 		Challenges:       req.Challenges,
// 		Timing:           req.Timing,
// 		PoiName:          req.PoiName,
// 		PoiId:            req.PoiId,
// 		ProductUrl:       req.ProductUrl,
// 		ShopDraftId:      updateResp.Data.ShopDraftId,
// 	}
// 	fmt.Printf("发布带货视频:发布视频:%v", createVodReq)
// 	resp, err = s.CreatePromotionVod(createVodReq)
// 	return
// }
