package ai

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MinimaxVoiceCloneRouter struct{}

// InitMinimaxVoiceCloneRouter 初始化 MiniMax 人声克隆路由
func (router *MinimaxVoiceCloneRouter) InitMinimaxVoiceCloneRouter(Router *gin.RouterGroup) {
	minimaxVoiceCloneRouter := Router.Group("ai/minimax-voice-clone").Use(middleware.OperationRecord())
	minimaxVoiceCloneRouterWithoutRecord := Router.Group("ai/minimax-voice-clone")
	minimaxVoiceCloneApi := v1.ApiGroupApp.AiApiGroup.MinimaxVoiceCloneApi

	{
		minimaxVoiceCloneRouter.POST("create", minimaxVoiceCloneApi.CreateMinimaxVoiceCloneTask) // 创建 MiniMax 人声克隆任务
	}
	{
		minimaxVoiceCloneRouterWithoutRecord.GET("status/:taskId", minimaxVoiceCloneApi.GetMinimaxVoiceCloneTaskStatus) // 获取任务状态
		minimaxVoiceCloneRouterWithoutRecord.GET("list", minimaxVoiceCloneApi.GetMinimaxVoiceCloneTaskList)             // 获取任务列表
		minimaxVoiceCloneRouterWithoutRecord.GET("voices", minimaxVoiceCloneApi.GetOfficialVoices)                      // 获取官方音色列表
	}
}
