package request

type MoreCreatorMixApiGoodsParseRequest struct {
	<PERSON>ie        string `json:"cookie"`
	Proxy         string `json:"proxy,omitempty"`
	PromotionLink string `json:"promotion_link"` // 商品链接

}

type MoreCreatorMixApiUpdatePromotionRequest struct {
	Cookie       string `json:"cookie"`
	Proxy        string `json:"proxy,omitempty"`
	PromotionId  string `json:"promotion_id"`
	ElasticTitle string `json:"elastic_title"`
	ElasticImg   string `json:"elastic_img"`
	ProductId    string `json:"product_id"`
}

type MoreCreatorMixApiRawDraft struct {
	PromotionId     string `json:"promotion_id"`
	ElasticTitle    string `json:"elastic_title"`
	ElasticImg      string `json:"elastic_img"`
	ProductId       string `json:"product_id"`
	PromotionSource int    `json:"promotion_source"`
	ThirdParty      int    `json:"third_party"`
}

type MoreCreatorMixApiMountPromotionRequest struct {
	Cookie    string                      `json:"cookie"`
	WebCookie string                      `json:"web_cookie"`
	Proxy     string                      `json:"proxy"`
	DraftId   string                      `json:"draft_id,omitempty"` // 第一个购物车的draft_id
	RawDraft  []MoreCreatorMixApiRawDraft `json:"raw_draft"`
}

type CreateVod struct {
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	ShopDraftId      string `json:"shop_draft_id"`
}

type MoreCreatorMixApiCreateVodRequest struct {
	Cookie           string `json:"cookie"`
	Proxy            string `json:"proxy,omitempty"`
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	ShopDraftId      string `json:"shop_draft_id"`
	MusicId          string `json:"music_id,omitempty"`
}

type MoreCreatorMixApiPromotion struct {
	Title string `json:"title"`
	Link  string `json:"link"`
}
type MoreCreatorMixApiCreatePromotionRequest struct {
	WebCookie  string                       `json:"web_cookie"`
	Cookie     string                       `json:"cookie"`
	Proxy      string                       `json:"proxy,omitempty"`
	Promotions []MoreCreatorMixApiPromotion `json:"promotions,omitempty"` // 商品信息

	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	MusicId          string `json:"music_id,omitempty"`
}

type MoreCreatorMixApiCreateNoteRequest struct {
	Cookie         string   `json:"cookie"`
	Proxy          string   `json:"proxy"`
	Description    string   `json:"description"`
	VisibilityType string   `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	UploadPoster   string   `json:"upload_poster" comment:"视频封面"`
	Images         []string `json:"images" comment:"图文文件列表"`
}

type MoreCreatorCustomApiMixCreateVodRequest struct {
	Cookie           string `json:"cookie"`
	Proxy            string `json:"proxy"`
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	MusicId          string `json:"music_id,omitempty"`
}
