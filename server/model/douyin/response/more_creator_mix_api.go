package response

type MoreCreatorMixApiGoodsParseResponse struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		Code  string `json:"code"`
		Msg   string `json:"msg"`
		Extra struct {
			Logid string `json:"logid"`
			Now   int64  `json:"now"`
		} `json:"extra"`
		StatusCode int                   `json:"status_code"`
		StatusMsg  string                `json:"status_msg"`
		Promotions []GoodsParsePromotion `json:"promotions"`
	}
}

type GoodsParsePromotion struct {
	Gid  string `json:"gid"`
	Imgs []struct {
		Uri     string   `json:"uri"`
		UrlList []string `json:"url_list"`
	} `json:"imgs"`
	PromotionId string `json:"promotion_id"`
}

type MoreCreatorMixApiUpdatePromotionResponse struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		Code        string `json:"code"`
		Msg         string `json:"msg"`
		StatusCode  int    `json:"status_code"` // 0表示成功
		StatusMsg   string `json:"status_msg"`
		ShopDraftId string `json:"shop_draft_id"`
	}
}

type MoreCreatorCustomApiMixCreateVodResponse struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		Extra struct {
			Logid string `json:"logid"`
			Now   int64  `json:"now"`
		} `json:"extra"`
		ItemId        string `json:"item_id"`
		StatusCode    int    `json:"status_code"` // 0表示成功
		StatusMessage string `json:"status_message"`
		StatusMsg     string `json:"status_msg"`
		EncryptUid    string `json:"encrypt_uid"`
	} `json:"data"`
}
