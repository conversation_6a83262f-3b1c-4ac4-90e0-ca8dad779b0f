package ai

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	VideoMultiLensApi
	VideoApi
	AutoVideoApi
	AutoImageTextApi
	VoiceCloneApi
	MinimaxVoiceCloneApi
	MinimaxVoiceFavoriteApi
}

var (
	videoMultiLensService    = service.ServiceGroupApp.AiServiceGroup.VideoMultiLensService
	videoService             = service.ServiceGroupApp.AiServiceGroup.VideoService
	autoVideoService         = service.ServiceGroupApp.AiServiceGroup.AutoVideoService
	autoImageTextService     = service.ServiceGroupApp.AiServiceGroup.AutoImageTextService
	voiceCloneService        = service.ServiceGroupApp.AiServiceGroup.VoiceCloneService
	minimaxVoiceCloneService = service.ServiceGroupApp.AiServiceGroup.MinimaxVoiceCloneService
	// 媒体服务
	musicCategoryService = service.ServiceGroupApp.MediaServiceGroup.MusicCategoryService
	imageCategoryService = service.ServiceGroupApp.MediaServiceGroup.ImageCategoryService
	videoCategoryService = service.ServiceGroupApp.MediaServiceGroup.VideoCategoryService
	// 媒体资源服务
	mediaVideoService = service.ServiceGroupApp.MediaServiceGroup.VideoService
	mediaImageService = service.ServiceGroupApp.MediaServiceGroup.ImageService
	mediaMusicService = service.ServiceGroupApp.MediaServiceGroup.MusicService
)
