<template>
  <!-- 抖音号搜索弹窗 -->
  <el-dialog v-model="searchDialogVisible" title="搜索抖音号" width="520px" center draggable @close="handleSearchClose">
    <div class="flex flex-col items-center gap-4">
      <el-form :model="searchForm" label-width="80px" class="w-full">
        <el-form-item label="账号类型">
          <el-radio-group v-model="searchForm.accountType">
            <el-radio :label="1">个人账号</el-radio>
            <el-radio :label="2">企业账号</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择分类">
          <el-tree-select
            v-model="searchForm.categoryId"
            :data="categories"
            check-strictly
            :props="defaultProps"
            :render-after-expand="false"
            style="width: 100%"
            placeholder="请选择分类"
          />
        </el-form-item>
        <el-form-item label="抖音号">
          <el-input v-model="searchForm.uniqueId" placeholder="请输入抖音号" />
        </el-form-item>
        <!-- 增加一列代理ip选项 -->
        <el-form-item label="代理IP">
          <el-input v-model="searchForm.proxy" placeholder="请获取代理IP" disabled />
          <!-- 增加两个文字按钮：1.搜索匹配 2.独立ip -->
          <el-button type="text" @click="searchProxyFit" style="font-size: 12px; text-decoration: underline">
            搜索匹配同手机ip
          </el-button>
          <el-button type="text" @click="getIndependentIp" style="font-size: 12px; text-decoration: underline">
            独立IP
          </el-button>
        </el-form-item>

        <el-form-item label="匹配代理" v-if="searchProxyFormItem">
          <!-- 在同一行增加两个元素，一个是下拉框fitProxyChannel：1-抖音号，2-代理端口。另一个是el-autocomplete组件，用于搜索匹配的代理 -->
          <el-col :span="6">
            <el-select v-model="fitProxyChannel" placeholder="请选择渠道">
              <el-option label="抖音号" value="1"></el-option>
              <el-option label="代理端口" value="2"></el-option>
              <el-option label="手机MAC地址" value="3"></el-option>
            </el-select>
          </el-col>
          <el-col :span="16">
            <el-autocomplete
              v-model="fitProxyKeyword"
              :fetch-suggestions="fitProxySearch"
              placeholder="请根据渠道输入关键信息"
              @select="handleFitProxySelect"
              :trigger-on-focus="true"
              popper-class="phone-suggestions"
              class="w-full"
            >
              <template #default="{ item }">
                {{
                  Number(fitProxyChannel) === 1
                    ? item.nickname
                    : Number(fitProxyChannel) === 2
                    ? item.bindIP
                    : item.nickname || item.bindIP + ' (匹配MAC)'
                }}
              </template>
            </el-autocomplete>
          </el-col>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleSearchConfirm">前往授权</el-button>
      <el-button @click="handleSearchClose">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 新添加授权弹窗 -->
  <el-dialog
    v-model="authDialogVisible"
    title="获取二维码"
    width="520px"
    center
    draggable
    @open="startPolling"
    @close="stopPolling"
  >
    <div class="flex flex-col items-center gap-4">
      <el-form :model="authForm" label-width="80px" class="w-full">
        <el-form-item label="uniqueId">
          <el-input v-model="authForm.uniqueId" disabled />
        </el-form-item>
        <el-form-item label="选择分类" disabled>
          <el-tree-select
            v-model="authForm.categoryId"
            :data="categories"
            check-strictly
            :props="defaultProps"
            :render-after-expand="false"
            style="width: 100%"
            placeholder="请选择分类"
          />
        </el-form-item>
        <el-form-item label="did">
          <el-input v-model="authForm.did" placeholder="请输入设备ID" disabled />
        </el-form-item>
        <el-form-item label="iid">
          <el-input v-model="authForm.iid" placeholder="请输入iid" disabled />
        </el-form-item>
        <el-form-item label="代理">
          <el-input v-model="authForm.proxy" placeholder="请输入代理" disabled />
        </el-form-item>
        <el-form-item label="扫码登录">
          <!-- 显示二维码图片 -->
          <div class="qr-code-container">
            <!-- 添加调试信息显示 -->
            <div v-if="!authForm.qr_code_url" style="color: red; font-size: 12px; margin-bottom: 8px">
              调试信息: qr_code_url值为: {{ authForm.qr_code_url || '空' }}
            </div>
            <img
              v-if="authForm.qr_code_url && authForm.qr_code_url.trim() !== ''"
              :src="`https://quickchart.io/qr?text=${encodeURIComponent(authForm.qr_code_url)}&size=150`"
              alt="二维码"
              class="qr-code-image"
            />
            <div v-else style="padding: 20px; text-align: center; color: #999">
              <p>等待获取二维码...</p>
              <el-input v-model="authForm.qr_code_url" disabled placeholder="二维码URL将显示在这里" />
            </div>
          </div>
        </el-form-item>
        <div v-if="authForm.warningText" class="warning-text">{{ authForm.warningText }}</div>
        <!-- 添加v-if指令来控制显示隐藏 -->
        <el-form-item label="验证码" v-if="authForm.showCaptchaInput">
          <el-input v-model="authForm.code" placeholder="请输入验证码" />
        </el-form-item>
        <el-form-item v-if="authForm.showCaptchaInput">
          <el-button @click="validSms">验证</el-button>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="handleAuthClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { fuzzyMatchUsersByProxyPort, fuzzyMatchUsersByUniqueId, fuzzyMatchUsersByMac } from '@/api/douyin/dyUser'
  import { getUnusedIP } from '@/api/douyin/ip'
  import {
    getQrCode,
    checkQrCode,
    sendCaptcha,
    validCaptcha,
    getUserInfoForMore,
    getUserLoginInfoForMore
  } from '@/api/douyin/douyinForMore'

  defineOptions({
    name: 'AccountAuthDialog'
  })

  const props = defineProps({
    searchVisible: {
      type: Boolean,
      default: false
    },
    authVisible: {
      type: Boolean,
      default: false
    },
    categories: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => null
    }
  })

  const emit = defineEmits(['update:searchVisible', 'update:authVisible', 'auth-success', 'clear-user-info'])

  const defaultProps = {
    children: 'children',
    label: 'name',
    value: 'ID'
  }

  // 搜索弹窗相关
  const searchForm = ref({
    uniqueId: null,
    accountType: 1,
    categoryId: null,
    proxy: null
  })

  const searchProxyFormItem = ref(false)
  const fitProxyChannel = ref(null)
  const fitProxyKeyword = ref(null)

  const searchDialogVisible = computed({
    get: () => props.searchVisible,
    set: (val) => emit('update:searchVisible', val)
  })

  // 授权弹窗相关
  const authForm = ref({
    categoryId: null,
    did: '',
    accountType: null,
    token: null,
    showCaptchaInput: null,
    iid: null,
    proxy: null,
    qr_code_url: null,
    code: null,
    warningText: null,
    uniqueId: null
  })

  const authDialogVisible = computed({
    get: () => props.authVisible,
    set: (val) => {
      // 添加调试信息
      console.log('authDialogVisible 变化:', val)
      if (val) {
        console.log('弹窗打开时 authForm 状态:', authForm.value)
      }
      emit('update:authVisible', val)
    }
  })

  const checkingQrCodeFlag = ref(false)

  const pollingData = ref({
    pollInterval: null,
    stopPollTimeout: null
  })

  const handleSearchClose = () => {
    // 重置表单
    searchForm.value = {
      uniqueId: null,
      accountType: 1,
      categoryId: null,
      proxy: null
    }
    searchProxyFormItem.value = false
    fitProxyChannel.value = null
    fitProxyKeyword.value = null
    searchDialogVisible.value = false
  }

  const handleSearchConfirm = async () => {
    // 验证表单
    if (!searchForm.value.uniqueId) {
      ElMessage.error('请填写抖音号')
      return
    }
    if (!searchForm.value.categoryId && searchForm.value.categoryId !== 0) {
      ElMessage.error('请选择分类')
      return
    }
    if (!searchForm.value.accountType && searchForm.value.accountType !== 0) {
      ElMessage.error('请选择账号类型')
      return
    }
    if (!searchForm.value.proxy) {
      ElMessage.error('请获取合适的代理')
      return
    }

    const res = await getUserInfoForMore({ uniqueId: searchForm.value.uniqueId })
    if (res.code !== 0) {
      ElMessage.error('获取用户资料失败:', res.code)
      return
    }
    if (res.data?.ID) {
      ElMessage.error('该用户已完成授权')
      handleSearchClose()
      return
    }

    // 设置授权表单数据
    authForm.value.uniqueId = searchForm.value.uniqueId
    authForm.value.categoryId = searchForm.value.categoryId
    authForm.value.accountType = searchForm.value.accountType
    authForm.value.proxy = searchForm.value.proxy

    // 关闭搜索弹窗，打开授权弹窗
    handleSearchClose()
    showAuthDialog()
  }

  const showAuthDialog = async () => {
    try {
      const response = await getQrCode({
        uniqueId: authForm.value.uniqueId,
        accountType: authForm.value.accountType,
        categoryId: authForm.value.categoryId,
        proxy: authForm.value.proxy
      })

      // 添加调试信息
      console.log('getQrCode API 响应:', response)
      console.log('response.data:', response.data)
      console.log('qr_code_url值:', response.data?.qr_code_url)

      if (response.code === 0) {
        authForm.value.token = response.data.token
        authForm.value.did = response.data.did
        authForm.value.iid = response.data.iid
        authForm.value.proxy = response.data.proxy
        authForm.value.qr_code_url = response.data.qr_code_url
        authForm.value.code = null

        // 添加调试信息
        console.log('设置后的authForm.qr_code_url:', authForm.value.qr_code_url)

        // 检查二维码URL是否为空
        if (!authForm.value.qr_code_url || authForm.value.qr_code_url.trim() === '') {
          authForm.value.warningText = '获取二维码失败：二维码URL为空，请重新尝试或联系管理员'
          ElMessage.warning('二维码URL为空，请重新尝试')
        } else {
          authForm.value.warningText = '请使用抖音扫码授权'
        }

        authDialogVisible.value = true
      } else {
        ElMessage.error('获取二维码失败：' + response.code)
      }
    } catch (err) {
      console.error('获取二维码请求异常:', err)
      ElMessage.error('请求出错：' + err.message)
    }
  }

  const handleAuthClose = async () => {
    await stopPolling()
    authDialogVisible.value = false
    authForm.value.showCaptchaInput = false
    authForm.value.token = null
    authForm.value.did = null
    authForm.value.iid = null
    authForm.value.proxy = null
    authForm.value.qr_code_url = null
    authForm.value.code = null
    authForm.value.warningText = null
    authForm.value.accountType = null
    authForm.value.categoryId = null
    authForm.value.uniqueId = null
    checkingQrCodeFlag.value = false

    // 通知父组件清空用户信息
    emit('clear-user-info')
  }

  const authQrCode = async () => {
    if (checkingQrCodeFlag.value) {
      return
    }
    const params = {
      token: authForm.value.token,
      categoryId: authForm.value.categoryId
    }
    if (authForm.value.code) {
      params.code = authForm.value.code
    }
    checkingQrCodeFlag.value = true
    const response = await checkQrCode(params)

    if (response.code === 0) {
      checkingQrCodeFlag.value = false
      if (response.data.status) {
        if (response.data.status === 'new') {
          authForm.value.warningText = '请使用抖音扫码授权'
          return
        } else if (response.data.status === 'expired') {
          authForm.value.warningText = '二维码已过期，请重新获取'
          ElMessage.error('二维码已过期，请重新获取')
          handleAuthClose()
          emit('auth-success')
          return
        } else if (response.data.status === 'scanned') {
          authForm.value.warningText = '已扫码，请在抖音界面确认登录'
          return
        } else {
          ElMessage.error('扫码状态异常退出：', response.data.status)
          handleAuthClose()
          emit('auth-success')
          return
        }
      }

      // 停止轮询操作
      await stopPolling()
      if (response.data && response.data.code === 2046) {
        const sendCaptchaRes = await sendCaptcha({ token: authForm.value.token })
        if (sendCaptchaRes.code === 0) {
          authForm.value.showCaptchaInput = true
          authForm.value.warningText = '短信已发送，请注意查收'
        } else {
          authForm.value.warningText = '发送短信验证码失败'
          ElMessage.error('发送短信验证码失败', sendCaptchaRes.msg)
        }
      } else {
        authForm.value.warningText = '授权成功'
        ElMessage.success('授权成功')
        const res = await getLoginInfo({ uniqueId: authForm.value.uniqueId }, 1, 0)
        if (res.code !== 0) {
          ElMessage.warning('新用户登录失效，请重新登录！')
        }
        handleAuthClose()
        emit('auth-success')
      }
    } else {
      ElMessage.error('err:', response.msg)
      if (
        response.msg === '账号注册完成，等待系统自动确认登录状态' ||
        response.msg === '刷新完成，系统将自动确认登录状态'
      ) {
        handleAuthClose()
        emit('auth-success')
        return
      }
    }
  }

  const getLoginInfo = async (row, loginForce = 0, chatForce = 0) => {
    const req = { uniqueId: row.uniqueId, loginForce: loginForce, chatForce: chatForce }
    try {
      return await getUserLoginInfoForMore(req)
    } catch (err) {
      throw new Error(`getLoginInfo异常：uniqueId:${row.uniqueId},err:${err.message}`)
    }
  }

  const validSms = async () => {
    if (!authForm.value.code && authForm.value.code === '') {
      ElMessage.error('请输入验证码')
      return
    }

    const params = {
      token: authForm.value.token,
      code: authForm.value.code
    }
    const response = await validCaptcha(params)
    if (response.code === 0) {
      ElMessage.success('校验成功,请稍后！')
      await authQrCode()
    } else {
      ElMessage.error('validSms:', response.msg)
    }
  }

  const startPolling = async () => {
    pollingData.value.pollInterval = setInterval(authQrCode, 3000)
    pollingData.value.stopPollTimeout = setTimeout(stopPolling, 120000)
  }

  const stopPolling = async () => {
    clearInterval(pollingData.value.pollInterval)
    clearTimeout(pollingData.value.stopPollTimeout)
    checkingQrCodeFlag.value = false
    pollingData.value.pollInterval = null
    pollingData.value.stopPollTimeout = null
  }

  const searchProxyFit = () => {
    searchProxyFormItem.value = true
    fitProxyChannel.value = null
    fitProxyKeyword.value = null
  }

  // 获取独立IP
  const getIndependentIp = async () => {
    try {
      const res = await getUnusedIP()
      if (res.code === 0 && res.data) {
        searchForm.value.proxy = res.data
      }
    } catch (err) {
      console.error('获取独立IP失败:', err)
      ElMessage.error('网络请求失败，请稍后重试')
    }
  }

  const fitProxySearch = async (queryString, cb) => {
    if (!queryString) {
      return cb([])
    }
    try {
      let response
      if (fitProxyChannel.value == 1) {
        // 抖音号匹配模式
        response = await fuzzyMatchUsersByUniqueId({ uniqueId: queryString })
      } else if (fitProxyChannel.value == 2) {
        // 代理端口匹配模式
        response = await fuzzyMatchUsersByProxyPort({ port: queryString })
      } else if (fitProxyChannel.value == 3) {
        // MAC地址匹配模式
        response = await fuzzyMatchUsersByMac({ mac: queryString })
      } else {
        return cb([])
      }

      if (response.code === 0) {
        let results
        if (fitProxyChannel.value == 2) {
          // 代理端口搜索返回的是IP池信息
          results = response.data.map((item) => ({
            value: item.ip,
            bindIP: item.ip,
            nickname: `IP: ${item.ip} (城市: ${item.city || '未知'})`,
            ...item
          }))
        } else if (fitProxyChannel.value == 3) {
          // MAC地址搜索返回的是IP池信息
          results = response.data.map((item) => ({
            value: item.ip,
            bindIP: item.ip,
            nickname: `IP: ${item.ip} (城市: ${item.city || '未知'})`,
            ...item
          }))
        } else {
          // 其他搜索返回的是用户信息
          results = response.data.map((item) => ({
            value: fitProxyChannel.value == 1 ? item.nickname : item.bindIP,
            ...item
          }))
        }
        cb(results)
      } else {
        ElMessage.error(response.msg || '搜索失败')
        cb([])
      }
    } catch (e) {
      console.error('搜索异常:', e)
      cb([])
    }
  }

  // 添加选择处理函数
  const handleFitProxySelect = (item) => {
    searchForm.value.proxy = item.bindIP
  }

  // 监听userInfo变化，自动填充authForm并获取二维码
  watch(
    () => props.userInfo,
    async (newUserInfo) => {
      if (newUserInfo && newUserInfo.uniqueId) {
        console.log('接收到用户信息:', newUserInfo)

        // 填充authForm
        authForm.value.uniqueId = newUserInfo.uniqueId
        authForm.value.categoryId = newUserInfo.categoryId
        authForm.value.accountType = newUserInfo.accountType
        authForm.value.proxy = newUserInfo.bindIP

        // 自动获取二维码
        await showAuthDialog()
      }
    },
    { immediate: true }
  )
</script>

<style scoped>
  /* 警告文本样式 */
  .warning-text {
    color: #e6a23c;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    background: #fdf6ec;
    border: 1px solid #f5dab1;
    border-radius: 6px;
    margin: 12px 0;
    text-align: center;
  }

  /* 二维码容器样式 */
  .qr-code-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #e9ecef;
  }

  .qr-code-image {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }

  .qr-code-image:hover {
    transform: scale(1.05);
  }

  /* 表单样式优化 */
  .el-form {
    background: #fafbfc;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-form-item__label {
    font-weight: 600;
    color: #495057;
  }

  /* 按钮样式优化 */
  .el-button--text {
    margin-left: 8px;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .el-button--text:hover {
    background: #e3f2fd;
    color: #1976d2;
    text-decoration: none;
  }

  /* 对话框样式 */
  :deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
  }

  :deep(.el-dialog__title) {
    color: white;
    font-size: 16px;
    font-weight: 600;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background: #ffffff;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
    text-align: right;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .el-form {
      padding: 16px;
    }

    .qr-code-container {
      padding: 12px;
    }

    .qr-code-image {
      max-width: 100%;
      height: auto;
    }

    .warning-text {
      font-size: 13px;
      padding: 6px 10px;
    }
  }
</style>

<style>
  /* 全局样式：手机建议下拉列表 */
  .phone-suggestions {
    max-height: 200px;
    overflow-y: auto;
  }

  .phone-suggestions .el-autocomplete-suggestion__wrap {
    max-height: 200px;
  }

  .phone-suggestions .el-autocomplete-suggestion__list {
    padding: 4px 0;
  }

  .phone-suggestions .el-autocomplete-suggestion__list li {
    padding: 8px 12px;
    margin: 2px 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .phone-suggestions .el-autocomplete-suggestion__list li:hover {
    background: #e3f2fd;
    color: #1976d2;
  }

  .phone-suggestions .el-autocomplete-suggestion__list li.highlighted {
    background: #2196f3;
    color: white;
  }
</style>
