<template>
  <el-dialog v-model="dialogVisible" title="发布设定" width="800px" @close="handleClose" center draggable>
    <!-- 增加"日期模板" -->
    <div style="margin-top: 5px">
      <div class="flex items-center gap-2">
        <div class="section-title">日期模板：</div>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="template in publishTemplateNameList"
            :key="template.templateId"
            class="px-2 py-1 bg-blue-200 rounded flex items-center gap-2"
          >
            <span @click="chooseTemplate(template)">
              {{ template.templateName }}
            </span>
            <el-icon class="cursor-pointer mr-2" style="font-size: 14px" @click="editTemplate(template)">
              <Edit />
            </el-icon>
            <el-icon class="cursor-pointer" style="font-size: 14px" @click="deleteTemplate(template)">
              <Delete />
            </el-icon>
          </span>
        </div>
        <el-icon @click="showAutoPublishTemplateDialog"><CirclePlus /></el-icon>
      </div>
    </div>
    <div style="margin-top: 5px">
      <span class="section-title"
        >选择日期：
        <el-link type="danger" size="mini" @click="clearPublishData">
          <el-icon> <Delete /> </el-icon>清空
        </el-link>
        <el-link type="primary" size="mini" @click="handleResetPublishData" class="ml-2">
          <el-icon> <Refresh /> </el-icon>默认
        </el-link>
      </span>
    </div>
    <div style="margin-top: 5px">
      <el-tabs v-model="publishCurrentDay" type="border-card">
        <el-tab-pane label="周一" :name="1"></el-tab-pane>
        <el-tab-pane label="周二" :name="2"></el-tab-pane>
        <el-tab-pane label="周三" :name="3"></el-tab-pane>
        <el-tab-pane label="周四" :name="4"></el-tab-pane>
        <el-tab-pane label="周五" :name="5"></el-tab-pane>
        <el-tab-pane label="周六" :name="6"></el-tab-pane>
        <el-tab-pane label="周日" :name="7"></el-tab-pane>
        <div class="section-title">选择时段：</div>
        <el-checkbox-group v-model="currentPublishData[publishCurrentDay]">
          <el-checkbox :label="0">0点</el-checkbox>
          <el-checkbox :label="1">1点</el-checkbox>
          <el-checkbox :label="2">2点</el-checkbox>
          <el-checkbox :label="3">3点</el-checkbox>
          <el-checkbox :label="4">4点</el-checkbox>
          <el-checkbox :label="5">5点</el-checkbox>
          <el-checkbox :label="6">6点</el-checkbox>
          <el-checkbox :label="7">7点</el-checkbox>
          <el-checkbox :label="8">8点</el-checkbox>
          <el-checkbox :label="9">9点</el-checkbox>
          <el-checkbox :label="10">10点</el-checkbox>
          <el-checkbox :label="11">11点</el-checkbox>
          <el-checkbox :label="12">12点</el-checkbox>
          <el-checkbox :label="13">13点</el-checkbox>
          <el-checkbox :label="14">14点</el-checkbox>
          <el-checkbox :label="15">15点</el-checkbox>
          <el-checkbox :label="16">16点</el-checkbox>
          <el-checkbox :label="17">17点</el-checkbox>
          <el-checkbox :label="18">18点</el-checkbox>
          <el-checkbox :label="19">19点</el-checkbox>
          <el-checkbox :label="20">20点</el-checkbox>
          <el-checkbox :label="21">21点</el-checkbox>
          <el-checkbox :label="22">22点</el-checkbox>
          <el-checkbox :label="23">23点</el-checkbox>
        </el-checkbox-group>
      </el-tabs>
    </div>
    <div style="margin-top: 5px">
      <span class="section-title">选择类型：</span>
      <el-radio-group v-model="publishCurrentType">
        <el-radio :label="1">视频</el-radio>
        <el-radio :label="2">图文</el-radio>
      </el-radio-group>
    </div>
    <div style="margin-top: 5px">
      <span>选择类库：</span>
      <el-cascader
        v-model="publishCurrentVideoCategoryIds"
        :options="videoCategories"
        :props="{ checkStrictly: true, emitPath: true, value: 'ID', label: 'name' }"
        placeholder="请选择视频分类"
        clearable
        style="width: 300px"
      />
    </div>
    <div style="margin-top: 5px">
      <span>当前状态：</span>
      <span v-if="currentPublishUser?.autoPublishStatus === 0" class="text-gray-500"
        ><el-icon>
          <CircleClose />
        </el-icon>
        未配置</span
      >
      <span v-else-if="currentPublishUser?.autoPublishStatus === 1" class="text-green-500"
        ><el-icon>
          <CircleCheck />
        </el-icon>
        已配置</span
      >
      <span v-else-if="currentPublishUser?.autoPublishStatus === 2" class="text-red-500"
        ><el-icon>
          <Warning />
        </el-icon>
        禁用</span
      >
    </div>
    <div style="margin-top: 5px">
      <span>状态设置：</span>
      <el-button-group v-if="currentPublishUser?.autoPublishStatus === 1">
        <el-button
          size="small"
          type="warning"
          @click="
            ElMessageBox.confirm('确定要禁用该账号的自动发布功能吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => handleChangeAutoPublishStatus(2))
          "
          style="margin-right: 1px"
          >禁用</el-button
        >
        <el-button
          size="small"
          type="danger"
          @click="
            ElMessageBox.confirm('确定要删除该账号的自动发布配置吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => handleChangeAutoPublishStatus(3))
          "
          style="margin-left: 1px"
          >删除</el-button
        >
      </el-button-group>
      <el-button-group v-else-if="currentPublishUser?.autoPublishStatus === 2">
        <el-button
          size="small"
          type="success"
          @click="
            ElMessageBox.confirm('确定要启用该账号的自动发布功能吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => handleChangeAutoPublishStatus(1))
          "
          style="margin-right: 1px"
          >启用</el-button
        >
        <el-button
          size="small"
          type="danger"
          @click="
            ElMessageBox.confirm('确定要删除该账号的自动发布配置吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => handleChangeAutoPublishStatus(3))
          "
          style="margin-left: 1px"
          >删除</el-button
        >
      </el-button-group>
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleAutoPublish">确定</el-button>
    </template>
  </el-dialog>

  <!-- 设置发布模板弹窗 -->
  <el-dialog
    v-model="autoPublishTemplateDialogVisible"
    title="发布时间模板"
    width="800px"
    @close="closeAutoPublishTemplateDialog"
    center
    draggable
  >
    <!-- 增加一列输入框："模板名称" -->
    <div style="margin-top: 5px">
      <div class="flex items-center gap-2">
        <div class="section-title">模板名称：</div>
        <el-input v-model="publishTemplateCurrentName" placeholder="请输入模板名称" style="width: 200px" />
      </div>
    </div>
    <div style="margin-top: 5px">
      <span class="section-title"
        >选择日期：
        <el-link type="danger" size="mini" @click="clearPublishTemplateData">
          <el-icon> <Delete /> </el-icon>清空
        </el-link>
      </span>
    </div>
    <div style="margin-top: 5px">
      <el-tabs v-model="publishTemplateCurrentDay" type="border-card">
        <el-tab-pane label="周一" :name="1"></el-tab-pane>
        <el-tab-pane label="周二" :name="2"></el-tab-pane>
        <el-tab-pane label="周三" :name="3"></el-tab-pane>
        <el-tab-pane label="周四" :name="4"></el-tab-pane>
        <el-tab-pane label="周五" :name="5"></el-tab-pane>
        <el-tab-pane label="周六" :name="6"></el-tab-pane>
        <el-tab-pane label="周日" :name="7"></el-tab-pane>
        <div class="section-title">选择时段：</div>
        <el-checkbox-group v-model="currentPublishTemplateData[publishTemplateCurrentDay]">
          <el-checkbox :label="0">0点</el-checkbox>
          <el-checkbox :label="1">1点</el-checkbox>
          <el-checkbox :label="2">2点</el-checkbox>
          <el-checkbox :label="3">3点</el-checkbox>
          <el-checkbox :label="4">4点</el-checkbox>
          <el-checkbox :label="5">5点</el-checkbox>
          <el-checkbox :label="6">6点</el-checkbox>
          <el-checkbox :label="7">7点</el-checkbox>
          <el-checkbox :label="8">8点</el-checkbox>
          <el-checkbox :label="9">9点</el-checkbox>
          <el-checkbox :label="10">10点</el-checkbox>
          <el-checkbox :label="11">11点</el-checkbox>
          <el-checkbox :label="12">12点</el-checkbox>
          <el-checkbox :label="13">13点</el-checkbox>
          <el-checkbox :label="14">14点</el-checkbox>
          <el-checkbox :label="15">15点</el-checkbox>
          <el-checkbox :label="16">16点</el-checkbox>
          <el-checkbox :label="17">17点</el-checkbox>
          <el-checkbox :label="18">18点</el-checkbox>
          <el-checkbox :label="19">19点</el-checkbox>
          <el-checkbox :label="20">20点</el-checkbox>
          <el-checkbox :label="21">21点</el-checkbox>
          <el-checkbox :label="22">22点</el-checkbox>
          <el-checkbox :label="23">23点</el-checkbox>
        </el-checkbox-group>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="closeAutoPublishTemplateDialog">取消</el-button>
      <el-button type="primary" @click="handleAutoPublishTemplate">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, watch, nextTick, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    getAutoPublishVideoListByDyUserId,
    saveAutoPublishVideo,
    changeAutoPublishVideoStauts
  } from '@/api/creative/autoPublishVideo'
  import {
    getPublishVideoTemplateNameList,
    getPublishVideoTemplateList,
    savePublishVideoTemplate,
    deletePublishVideoTemplate
  } from '@/api/creative/autoPublishVideoTemplate'

  defineOptions({
    name: 'AutoPublishDialog'
  })

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    user: {
      type: Object,
      default: () => ({})
    },
    videoCategories: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update:visible', 'success'])

  // 创建计算属性处理双向绑定
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 内部响应式数据
  const currentPublishUser = ref(null)
  const publishCurrentDay = ref(1)
  const publishCurrentType = ref(1)
  const publishCurrentVideoCategoryIds = ref([])
  const currentPublishData = ref({
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
    6: [],
    7: []
  })

  // 模板相关数据
  const autoPublishTemplateDialogVisible = ref(false)
  const publishTemplateCurrentDay = ref(1)
  const publishTemplateCurrentId = ref(null)
  const publishTemplateCurrentName = ref('')
  const currentPublishTemplateData = ref({
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
    6: [],
    7: []
  })
  const publishTemplateNameList = ref([])

  // 默认发布日期和时间
  const defaultPublishData = {
    1: [7, 11, 17],
    2: [8, 12, 18],
    3: [9, 13, 19],
    4: [7, 11, 20],
    5: [11, 17, 21],
    6: [7, 11, 16, 22],
    7: [7, 11, 16, 20]
  }

  // 获取发布模板名称列表
  const getPushlishTemplateNameList = async () => {
    const res = await getPublishVideoTemplateNameList()
    if (res.code === 0) {
      publishTemplateNameList.value = res.data
    }
  }

  // 新增：递归查找分类路径
  const findCategoryPath = (categoryId, categories) => {
    for (const category of categories) {
      if (category.ID === categoryId) {
        return [category.ID]
      }
      if (category.children) {
        const path = findCategoryPath(categoryId, category.children)
        if (path) {
          return [category.ID, ...path]
        }
      }
    }
    return null
  }

  // 初始化发布设定数据
  const initializePublishData = async () => {
    if (!props.user || !props.user.ID) return

    await getPushlishTemplateNameList()

    const res = await getAutoPublishVideoListByDyUserId({
      dyUserId: props.user.ID
    })

    if (res.code !== 0) {
      ElMessage.error('获取自动发布列表失败：' + res.msg)
      return
    }

    currentPublishUser.value = props.user

    if (res.data && res.data.length > 0) {
      const firstItem = res.data[0]
      publishCurrentType.value = firstItem.type
      if (firstItem.videoCategoryId) {
        const path = findCategoryPath(firstItem.videoCategoryId, props.videoCategories)
        if (path) {
          publishCurrentVideoCategoryIds.value = path
        }
      }

      // 重置时间数据
      currentPublishData.value = {
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: []
      }

      for (const item of res.data) {
        if (!publishCurrentDay.value) publishCurrentDay.value = item.weekday
        currentPublishData.value[item.weekday].push(item.hour)
      }
    } else {
      currentPublishData.value = JSON.parse(JSON.stringify(defaultPublishData))
    }

    if (!publishCurrentDay.value) publishCurrentDay.value = 1
    if (!publishCurrentType.value) publishCurrentType.value = 1
  }

  // 重置发布日期
  const handleResetPublishData = () => {
    currentPublishData.value = JSON.parse(JSON.stringify(defaultPublishData))
    publishCurrentDay.value = 1
  }

  // 清空发布数据
  const clearPublishData = () => {
    currentPublishData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  // 处理自动发布
  const handleAutoPublish = async () => {
    const list = []
    let videoCategoryId = null

    if (publishCurrentVideoCategoryIds.value && publishCurrentVideoCategoryIds.value.length > 0) {
      videoCategoryId = publishCurrentVideoCategoryIds.value[publishCurrentVideoCategoryIds.value.length - 1]
    }

    if (!videoCategoryId) {
      ElMessage.error('请选择一个类库')
      return
    }

    for (const [weekday, hours] of Object.entries(currentPublishData.value)) {
      for (const hour of hours) {
        list.push({
          weekday: parseInt(weekday),
          hour: parseInt(hour),
          type: publishCurrentType.value,
          videoCategoryId: videoCategoryId
        })
      }
    }

    const res = await saveAutoPublishVideo({
      dyUserId: currentPublishUser.value.ID,
      list
    })

    if (res.code === 0) {
      ElMessage.success('自动发布计划设定成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error('自动发布计划设定失败：' + res.msg)
    }
  }

  // 修改发布状态
  const handleChangeAutoPublishStatus = async (opt) => {
    const res = await changeAutoPublishVideoStauts({
      opt,
      dyUserId: currentPublishUser.value.ID
    })

    if (res.code === 0) {
      ElMessage.success('操作成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error('操作失败')
    }
  }

  // 关闭弹窗
  const handleClose = () => {
    emit('update:visible', false)
    // 重置数据
    currentPublishUser.value = null
    publishCurrentDay.value = 1
    publishCurrentType.value = 1
    publishCurrentVideoCategoryIds.value = []
    currentPublishData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  // 模板相关方法
  const showAutoPublishTemplateDialog = (row = {}) => {
    publishTemplateCurrentId.value = row.templateId || null
    publishTemplateCurrentName.value = row.templateName || ''

    if (publishTemplateCurrentId.value) {
      loadTemplateData(row.templateId)
    } else {
      // 新建模板时重置数据
      currentPublishTemplateData.value = {
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: []
      }
    }

    publishTemplateCurrentDay.value = 1
    autoPublishTemplateDialogVisible.value = true
  }

  const loadTemplateData = async (templateId) => {
    const res = await getPublishVideoTemplateList({ templateId })
    if (res.code !== 0) {
      ElMessage.error('获取自动发布模板详情失败：' + res.msg)
      closeAutoPublishTemplateDialog()
      return
    }

    currentPublishTemplateData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }

    if (res.data && res.data.length > 0) {
      for (const item of res.data) {
        currentPublishTemplateData.value[item.weekday].push(item.hour)
      }
    }
  }

  const closeAutoPublishTemplateDialog = async () => {
    autoPublishTemplateDialogVisible.value = false
    publishTemplateCurrentDay.value = 1
    publishTemplateCurrentId.value = null
    publishTemplateCurrentName.value = ''
    currentPublishTemplateData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
    await getPushlishTemplateNameList()
  }

  const clearPublishTemplateData = () => {
    currentPublishTemplateData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  const handleAutoPublishTemplate = async () => {
    const list = []
    if (!publishTemplateCurrentName.value) {
      ElMessage.error('请输入模板名称')
      return
    }

    for (const [weekday, hours] of Object.entries(currentPublishTemplateData.value)) {
      for (const hour of hours) {
        list.push({
          weekday: parseInt(weekday),
          hour: parseInt(hour),
          templateName: publishTemplateCurrentName.value
        })
      }
    }

    if (list.length === 0) {
      ElMessage.error('请选择发布时段')
      return
    }

    const data = { list }
    if (publishTemplateCurrentId.value) {
      data.oldTemplateId = publishTemplateCurrentId.value
    }

    const res = await savePublishVideoTemplate(data)
    if (res.code === 0) {
      ElMessage.success('操作成功')
      closeAutoPublishTemplateDialog()
    } else {
      ElMessage.error('操作失败：' + res.msg)
    }
  }

  // 编辑模板
  const editTemplate = async (row) => {
    await showAutoPublishTemplateDialog(row)
  }

  // 删除模板
  const deleteTemplate = async (row) => {
    try {
      await ElMessageBox.confirm('确认删除该模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await deletePublishVideoTemplate({ templateId: row.templateId })
      if (res.code === 0) {
        ElMessage.success('删除模板成功')
        await getPushlishTemplateNameList()
      } else {
        ElMessage.error('删除模板失败：' + res.msg)
      }
    } catch (err) {
      if (err !== 'cancel') {
        console.error('删除模板失败:', err)
        ElMessage.error('删除模板失败')
      }
    }
  }

  // 选择模板
  const chooseTemplate = async (row) => {
    try {
      await ElMessageBox.confirm('确认选择该模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await getPublishVideoTemplateList({ templateId: row.templateId })
      if (res.code !== 0) {
        ElMessage.error('获取模板详情失败：' + res.msg)
        return
      }

      if (res.data && res.data.length > 0) {
        let chooseDay = 1
        currentPublishData.value = {
          1: [],
          2: [],
          3: [],
          4: [],
          5: [],
          6: [],
          7: []
        }

        for (const item of res.data) {
          if (!chooseDay) chooseDay = item.weekday
          currentPublishData.value[item.weekday] = currentPublishData.value[item.weekday] || []
          currentPublishData.value[item.weekday].push(item.hour)
        }

        publishCurrentDay.value = chooseDay
        ElMessage.success('模板已选择')
      }
    } catch (err) {
      if (err !== 'cancel') {
        console.error('选择模板失败:', err)
        ElMessage.error('选择模板失败')
      }
    }
  }

  // 监听 visible 变化
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal && props.user) {
        nextTick(() => {
          initializePublishData()
        })
      }
    }
  )
</script>

<style scoped>
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #409eff;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(64, 158, 255, 0.2);
  }
</style>
