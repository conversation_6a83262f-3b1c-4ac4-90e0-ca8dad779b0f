<template>
  <div>
    <!-- 表格区域 -->
    <el-table
      ref="tableRef"
      :data="tableData"
      border
      stripe
      class="w-full"
      :header-cell-class-name="'bg-gray-50 dark:bg-slate-800'"
      row-key="ID"
    >
      <el-table-column align="center" label="基本信息" width="230">
        <template #default="scope">
          <div class="flex items-center space-x-2">
            <el-avatar :size="40" :src="getAvatarUrl(scope.row.avatar)" />
            <div class="flex flex-col items-start overflow-hidden">
              <span class="font-medium truncate w-full"
                >{{ scope.row.nickname }}
                <!-- 如果accountType为2,则显示一个蓝色V标 -->
                <el-tag v-if="scope.row.accountType === 2" size="mini" type="primary" class="ml-1">V</el-tag>
              </span>
              <div class="flex items-center gap-1">
                <span class="text-gray-600 text-xs">抖音号:</span>
                <span class="text-gray-500 text-sm truncate">{{ scope.row.uniqueId }}</span>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="copyUniqueId(scope.row.uniqueId)"
                  style="font-size: 12px; padding: 2px 4px; min-height: auto"
                >
                  复制
                </el-button>
              </div>
              <div class="flex items-center gap-1" v-if="scope.row.uid">
                <span class="text-gray-600 text-xs">UID:</span>
                <span class="text-gray-400 text-xs truncate">{{ scope.row.uid }}</span>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="copyUid(scope.row.uid)"
                  style="font-size: 10px; padding: 1px 3px; min-height: auto"
                >
                  复制
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="分类" width="120">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="$emit('update-category', scope.row)"
            :class="{ 'text-gray-400': !getCategoryExists(scope.row.categoryId) }"
          >
            {{ getCategoryName(scope.row.categoryId) }}
          </el-button>
        </template>
      </el-table-column>

      <el-table-column align="center" label="数据统计" width="130">
        <template #default="scope">
          <div class="flex flex-col gap-1">
            <div class="text-sm">
              <span>作品：</span>
              <span class="text-blue-500">{{ formatNumber(scope.row.awemeCount) }}</span>
            </div>
            <div class="text-sm">
              <span>粉丝：</span>
              <span class="text-blue-500">{{ formatNumber(scope.row.followerCount) }}</span>
            </div>
            <div class="text-sm">
              <span>可提现：</span>
              <span class="text-green-500">¥{{ formatNumber(scope.row.withdrawableAmount / 100) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="销售数据" width="130">
        <template #default="scope">
          <div class="flex flex-col gap-1">
            <div class="text-sm">
              <span>昨日：</span>
              <span class="text-orange-500">¥{{ formatNumber(scope.row.day1TotalAmount / 100) }}</span>
            </div>
            <div class="text-sm">
              <span>七日：</span>
              <span class="text-purple-500">¥{{ formatNumber(scope.row.day7TotalAmount / 100) }}</span>
            </div>
            <div class="text-sm">
              <span>总计：</span>
              <span class="text-red-500">¥{{ formatNumber(scope.row.totalAmount / 100) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="left" label="账号状态" width="160">
        <template #default="scope">
          <div class="flex flex-col gap-1">
            <div class="text-sm text-gray-600">
              <span>录入人员：{{ scope.row.sysUserName }}</span>
            </div>
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-600">
                <span
                  >登录授权：
                  <el-tag size="small" type="success" v-if="scope.row.status === 1">正常</el-tag>
                  <el-tag size="small" type="info" v-else-if="scope.row.status === 2">失效</el-tag>
                  <el-button
                    size="small"
                    type="info"
                    v-else-if="scope.row.status === 3"
                    @click="$emit('check-login-status', scope.row)"
                    >超时重试中</el-button
                  >
                  <el-button size="small" type="danger" v-else @click="$emit('check-login-status', scope.row)"
                    >未登录</el-button
                  >
                </span>
              </div>
            </div>
            <!-- 增加"私信授权" -->
            <div class="text-sm text-gray-600">
              <span
                >营销授权：
                <el-tag size="small" type="info" v-if="scope.row.talkAuthStatus === 0">未授权</el-tag>
                <el-tag size="small" type="success" v-if="scope.row.talkAuthStatus === 1">正常</el-tag>
                <el-tag size="small" type="warning" v-else-if="scope.row.talkAuthStatus === 2">已失效</el-tag>
                <el-tag size="small" type="danger" v-else-if="scope.row.talkAuthStatus === 3">禁用</el-tag>
              </span>
            </div>
            <div class="text-sm text-gray-500">
              <span
                >发布设定：
                <el-tag
                  size="small"
                  :type="
                    scope.row.autoPublishStatus === 1
                      ? 'success'
                      : scope.row.autoPublishStatus === 2
                      ? 'danger'
                      : 'info'
                  "
                >
                  {{
                    scope.row.autoPublishStatus === 0
                      ? '未配置'
                      : scope.row.autoPublishStatus === 1
                      ? videoCategoryMap && scope.row.videoCategoryId && videoCategoryMap[scope.row.videoCategoryId]
                        ? videoCategoryMap[scope.row.videoCategoryId]
                        : '未知分类'
                      : scope.row.autoPublishStatus === 2
                      ? '禁用'
                      : '未配置'
                  }}
                </el-tag>
              </span>
            </div>

            <div class="text-sm text-gray-600">
              <span
                >营销设定：
                <el-tag
                  size="small"
                  :type="
                    scope.row.commentTraceStatus === 1
                      ? 'success'
                      : scope.row.commentTraceStatus === 2
                      ? 'danger'
                      : 'info'
                  "
                >
                  {{
                    scope.row.commentTraceStatus === 0
                      ? '未配置'
                      : scope.row.commentTraceStatus === 1
                      ? '已配置'
                      : scope.row.commentTraceStatus === 2
                      ? '禁用'
                      : '未知'
                  }}
                </el-tag>
              </span>
            </div>

            <div>
              <el-button
                :type="scope.row.bindIP ? 'success' : 'warning'"
                size="small"
                @click="$emit('show-bind-info', scope.row)"
              >
                {{ scope.row.bindIP ? '绑定信息' : '未绑定' }}
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="实名认证" width="150">
        <template #default="scope">
          <div class="flex flex-col gap-1">
            <div class="text-sm">
              账号实名:
              <el-button
                size="small"
                type="text"
                @click="$emit('real-name-action', scope.row)"
                style="padding-left: 0px"
              >
                {{ scope.row.realName || '未实名' }}
              </el-button>
            </div>
            <div class="text-sm">
              绑定手机:
              <el-button size="small" type="text" @click="$emit('phone-action', scope.row)" style="padding-left: 0px">
                {{ scope.row.bindPhone || '未绑定' }}
              </el-button>
              <!-- 添加运营商和实名信息 -->
              <div v-if="scope.row.bindPhone" class="text-xs text-gray-500">
                {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="备注" width="220">
        <template #default="scope">
          <el-tooltip :content="scope.row.remark || '添加备注'" placement="top" :show-after="500">
            <div class="remark-container" @click="$emit('show-remark', scope.row)">
              <div v-if="scope.row.remark" class="remark-content">
                <div class="remark-text">
                  {{ scope.row.remark }}
                </div>
                <el-icon class="remark-icon">
                  <Edit />
                </el-icon>
              </div>
              <div v-else class="remark-empty">
                <span class="remark-placeholder">添加备注</span>
                <el-icon class="remark-icon">
                  <Edit />
                </el-icon>
              </div>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- 拖拽手柄列 -->
      <el-table-column align="center" label="排序" width="60">
        <template #default="scope">
          <div class="flex flex-col items-center gap-1 sort-buttons">
            <!-- 上移按钮 -->
            <el-button
              size="small"
              type="primary"
              :disabled="scope.$index === 0"
              @click="$emit('move-up', scope.$index)"
              style="padding: 2px 4px; min-height: auto"
            >
              <el-icon size="12">
                <ArrowUp />
              </el-icon>
            </el-button>

            <!-- 拖拽手柄 -->
            <div
              class="drag-handle cursor-move text-gray-400 hover:text-gray-600 flex items-center justify-center"
              style="padding: 4px"
            >
              <el-icon size="14">
                <Rank />
              </el-icon>
            </div>

            <!-- 下移按钮 -->
            <el-button
              size="small"
              type="primary"
              :disabled="scope.$index === tableData.length - 1"
              @click="$emit('move-down', scope.$index)"
              style="padding: 2px 4px; min-height: auto"
            >
              <el-icon size="12">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="240" fixed="right">
        <template #default="scope">
          <div class="operation-buttons">
            <div class="button-row">
              <el-button type="warning" size="small" @click="$emit('show-auto-publish', scope.row)">
                发布设定
              </el-button>
              <el-button type="warning" size="small" @click="$emit('show-content-setting', scope.row)">
                营销设定
              </el-button>
            </div>
            <div class="button-row">
              <el-button type="warning" size="small" @click="$emit('single-flush', scope.row)"> 刷新数据 </el-button>
              <el-button type="warning" size="small" @click="$emit('view-awemes', scope.row)"> 作品管理 </el-button>
            </div>
            <div class="button-row">
              <el-dropdown>
                <el-button type="primary" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="$emit('show-user-detail', scope.row)">
                      <span class="text-blue-500">查看详情</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="$emit('show-transfer', scope.row)">
                      <span class="text-orange-500">账号转移</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      divided
                      v-if="scope.row.isProductEnabled"
                      @click="$emit('disable-product', scope.row)"
                    >
                      <span class="text-red-500">关闭选品</span>
                    </el-dropdown-item>
                    <el-dropdown-item v-else @click="$emit('product-action', scope.row)">
                      <span class="text-green-500">开始选品</span>
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="$emit('delete-user', scope.row)">
                      <span class="text-red-500">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="flex justify-end mt-4">
      <el-pagination
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="$emit('current-change', $event)"
        @size-change="$emit('size-change', $event)"
        background
      />
    </div>
  </div>
</template>

<script setup>
  import { ref, nextTick, onMounted, onUnmounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { ArrowUp, ArrowDown, Rank, Edit } from '@element-plus/icons-vue'
  import Sortable from 'sortablejs'

  defineOptions({
    name: 'AccountTable'
  })

  // Props
  defineProps({
    tableData: {
      type: Array,
      default: () => []
    },
    page: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 100
    },
    total: {
      type: Number,
      default: 0
    },
    videoCategoryMap: {
      type: Object,
      default: () => ({})
    },
    getCategoryName: {
      type: Function,
      required: true
    },
    getCategoryExists: {
      type: Function,
      required: true
    }
  })

  // Emits
  const emit = defineEmits([
    'current-change',
    'size-change',
    'update-category',
    'check-login-status',
    'show-bind-info',
    'real-name-action',
    'phone-action',
    'show-remark',
    'move-up',
    'move-down',
    'show-auto-publish',
    'show-content-setting',
    'single-flush',
    'view-awemes',
    'show-user-detail',
    'show-transfer',
    'disable-product',
    'product-action',
    'delete-user',
    'save-sort'
  ])

  const tableRef = ref(null)
  const sortableInstance = ref(null)

  // 初始化拖拽排序
  const initSortable = () => {
    if (!tableRef.value) return

    // 如果已经初始化过，先销毁
    if (sortableInstance.value) {
      sortableInstance.value.destroy()
    }

    const tbody = tableRef.value.$el.querySelector('tbody')
    if (!tbody) return

    sortableInstance.value = Sortable.create(tbody, {
      animation: 150,
      handle: '.drag-handle', // 只能通过拖拽手柄进行拖拽
      onEnd: async (evt) => {
        // 获取新的排序
        const newIndex = evt.newIndex
        const oldIndex = evt.oldIndex

        if (newIndex === oldIndex) return

        // 通知父组件处理排序
        emit('save-sort', { oldIndex, newIndex })
      }
    })
  }

  // 数字格式化
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    }
    return num
  }

  // 获取头像URL
  const getAvatarUrl = (avatarJson) => {
    try {
      if (!avatarJson) return ''
      const avatarData = JSON.parse(avatarJson)
      return avatarData.url_list?.[0] || ''
    } catch (err) {
      console.error('解析头像JSON失败:', err)
      return ''
    }
  }

  // 复制抖音号功能
  const copyUniqueId = async (uniqueId) => {
    try {
      await navigator.clipboard.writeText(uniqueId)
      ElMessage.success('抖音号已复制到剪贴板')
    } catch {
      // 如果剪贴板API不可用，使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = uniqueId
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        ElMessage.success('抖音号已复制到剪贴板')
      } catch {
        ElMessage.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    }
  }

  // 复制uid功能
  const copyUid = async (uid) => {
    try {
      await navigator.clipboard.writeText(uid)
      ElMessage.success('UID已复制到剪贴板')
    } catch {
      // 如果剪贴板API不可用，使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = uid
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        ElMessage.success('UID已复制到剪贴板')
      } catch {
        ElMessage.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    }
  }

  // 组件挂载时初始化拖拽排序
  onMounted(async () => {
    await nextTick()
    initSortable()
  })

  // 组件卸载时销毁拖拽排序实例
  onUnmounted(() => {
    if (sortableInstance.value) {
      sortableInstance.value.destroy()
    }
  })

  // 暴露重新初始化拖拽的方法给父组件
  defineExpose({
    initSortable
  })
</script>

<style scoped>
  .text-sm {
    font-size: 13px;
    line-height: 1.5;
  }

  .drag-handle {
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .drag-handle:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .drag-handle:active {
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* 移动端优化 */
  @media (max-width: 768px) {
    .drag-handle {
      padding: 8px;
      font-size: 16px;
    }

    /* 移动端排序按钮优化 */
    .sort-buttons .el-button {
      padding: 1px 3px !important;
      min-height: 20px !important;
    }

    .sort-buttons .el-icon {
      font-size: 10px !important;
    }
  }

  /* 操作按钮样式优化 */
  .operation-buttons {
    display: flex;
    flex-direction: column;
    gap: 6px;
    align-items: center;
    padding: 8px 4px;
    min-height: 100px;
    justify-content: center;
  }

  .button-row {
    display: flex;
    gap: 6px;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .button-row .el-button {
    min-width: 80px;
    height: 28px;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    flex: 0 0 auto;
  }

  /* 确保所有按钮大小一致 */
  .operation-buttons .el-button {
    box-sizing: border-box;
  }

  /* 更多按钮样式调整 */
  .button-row:last-child {
    margin-top: 2px;
  }

  /* 移动端操作按钮适配 */
  @media (max-width: 768px) {
    .operation-buttons {
      gap: 4px;
      padding: 4px 2px;
      min-height: 80px;
    }

    .button-row .el-button {
      min-width: 70px;
      height: 24px;
      padding: 2px 6px;
      font-size: 11px;
    }

    .button-row {
      gap: 4px;
    }
  }

  /* 备注列样式优化 */
  .remark-container {
    cursor: pointer;
    padding: 8px 12px;
    margin: 4px;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    background-color: #fafafa;
    transition: all 0.2s ease;
    min-height: 50px;
    max-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .remark-container:hover {
    border-color: #409eff;
    background-color: #ecf5ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  }

  .remark-content {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }

  .remark-text {
    flex: 1;
    text-align: left;
    font-size: 13px;
    line-height: 1.4;
    color: #333;
    white-space: pre-line;
    word-break: break-word;
    max-height: 90px;
    overflow-y: auto;
    padding-right: 4px;
  }

  /* 自定义滚动条样式 */
  .remark-text::-webkit-scrollbar {
    width: 4px;
  }

  .remark-text::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  .remark-text::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  .remark-text::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  .remark-empty {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #999;
  }

  .remark-placeholder {
    font-size: 13px;
    color: #999;
  }

  .remark-icon {
    color: #409eff;
    font-size: 14px;
    flex-shrink: 0;
    transition: color 0.2s ease;
  }

  .remark-container:hover .remark-icon {
    color: #1e90ff;
  }

  /* 移动端备注列适配 */
  @media (max-width: 768px) {
    .remark-container {
      padding: 6px 10px;
      margin: 2px;
      min-height: 45px;
      max-height: 100px;
    }

    .remark-text {
      font-size: 12px;
      max-height: 75px;
    }

    .remark-placeholder {
      font-size: 12px;
    }

    .remark-icon {
      font-size: 13px;
    }
  }
</style>
