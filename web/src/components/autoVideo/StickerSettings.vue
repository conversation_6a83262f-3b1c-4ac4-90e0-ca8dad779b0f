<template>
  <div>
    <div class="setting-item" @click="showDialog = true">
      <el-icon>
        <PictureRounded />
      </el-icon>
      <span>贴纸设置</span>
    </div>

    <el-dialog v-model="showDialog" title="贴纸设置" width="800px" destroy-on-close draggable>
      <div class="popover-content">
        <div class="sticker-list">
          <el-empty
            v-if="!localStickers || localStickers.length === 0"
            description="暂未添加贴纸"
            style="padding: 10px 0"
          />
          <div v-else v-for="(sticker, index) in localStickers" :key="index" class="sticker-item">
            <div class="sticker-preview">
              <img
                v-if="sticker.thumbUrl || sticker.mediaUrl"
                :src="sticker.thumbUrl || sticker.mediaUrl"
                class="sticker-image"
              />
              <div v-else class="sticker-placeholder">未选择图片</div>
              <div class="sticker-controls">
                <el-button type="danger" size="small" circle @click="removeSticker(index)" class="delete-btn">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
              </div>
            </div>

            <div class="sticker-config">
              <div class="sticker-dimension-row">
                <div class="sticker-dimension-display-item">
                  <span class="dimension-label">宽度(px):</span>
                  <el-input :model-value="getDisplayPixelWidth(sticker)" size="small" readonly placeholder="自动计算" />
                </div>
                <div class="sticker-dimension-display-item">
                  <span class="dimension-label">高度(px):</span>
                  <el-input
                    :model-value="getDisplayPixelHeight(sticker)"
                    size="small"
                    readonly
                    placeholder="自动计算"
                  />
                </div>
              </div>

              <div class="sticker-size">
                <span class="size-label">大小(%):</span>
                <el-slider
                  v-model="sticker.size"
                  :min="5"
                  :max="50"
                  :step="1"
                  show-input
                  :format-tooltip="formatTooltip"
                />
              </div>

              <div class="sticker-coordinate-row">
                <div class="sticker-coordinate-display-item">
                  <span class="coordinate-label">X坐标(%):</span>
                  <el-input
                    v-model.number="sticker.coordinateX"
                    size="small"
                    placeholder="0-100"
                    type="number"
                    :min="0"
                    :max="100"
                    :step="0.01"
                    @input="handleCoordinateInput(index, 'X', $event)"
                    @blur="validateCoordinate(index, 'X')"
                  />
                  <div class="coordinate-slider-container">
                    <el-slider
                      v-model="sticker.coordinateX"
                      :min="0"
                      :max="100"
                      :step="0.1"
                      size="small"
                      :format-tooltip="formatCoordinateTooltip"
                    />
                  </div>
                </div>
                <div class="sticker-coordinate-display-item">
                  <span class="coordinate-label">Y坐标(%):</span>
                  <el-input
                    v-model.number="sticker.coordinateY"
                    size="small"
                    placeholder="0-100"
                    type="number"
                    :min="0"
                    :max="100"
                    :step="0.01"
                    @input="handleCoordinateInput(index, 'Y', $event)"
                    @blur="validateCoordinate(index, 'Y')"
                  />
                  <div class="coordinate-slider-container">
                    <el-slider
                      v-model="sticker.coordinateY"
                      :min="0"
                      :max="100"
                      :step="0.1"
                      size="small"
                      :format-tooltip="formatCoordinateTooltip"
                    />
                  </div>
                </div>
              </div>

              <div class="sticker-template-actions">
                <div class="template-selector">
                  <div class="template-header">
                    <span class="template-label">选择模板：</span>
                    <el-button
                      size="small"
                      @click="fetchStickerTemplates(sticker.ID)"
                      :loading="loadingTemplates"
                      plain
                    >
                      刷新模板
                    </el-button>
                  </div>

                  <div class="template-list" v-if="shouldShowTemplates(sticker.ID)">
                    <div
                      v-for="template in getTemplatesForSticker(sticker.ID)"
                      :key="template.ID"
                      class="template-item"
                      :class="{ 'template-selected': selectedTemplateIds[index] === template.ID }"
                      @click="applyTemplate(index, template.ID)"
                    >
                      <div class="template-content">
                        <div class="template-name">{{ template.name }}</div>
                        <div class="template-description" v-if="template.description">{{ template.description }}</div>
                        <div class="template-details">
                          尺寸: {{ template.width }}×{{ template.height }} | 坐标: ({{ template.x.toFixed(2) }},
                          {{ template.y.toFixed(2) }})
                        </div>
                      </div>
                      <div class="template-actions">
                        <el-button
                          type="danger"
                          size="small"
                          circle
                          @click.stop="deleteTemplate(template)"
                          title="删除模板"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <div class="template-empty" v-else-if="!loadingTemplates && isImageLoaded(sticker.ID)">
                    <el-empty description="该图片暂无可用模板" :image-size="60" />
                  </div>
                </div>
                <el-button type="success" size="small" @click="showStickerTemplateDialog(index)" plain>
                  <el-icon><Plus /></el-icon>
                  保存为模板
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <div class="add-sticker">
          <el-button type="primary" @click="openDrawer" plain>
            <el-icon>
              <Plus />
            </el-icon>
            从素材库添加贴纸
          </el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelChanges">取 消</el-button>
          <el-button type="primary" @click="confirmChanges">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 贴纸选择抽屉 (从素材库选择图片) -->
    <el-drawer
      v-model="showDrawer"
      title="从素材库选择图片作为贴纸"
      direction="rtl"
      size="60%"
      :body-style="{ padding: '0px' }"
    >
      <div class="sticker-picker">
        <!-- Add category sidebar -->
        <div class="picker-sidebar">
          <div class="category-list">
            <div
              class="category-item"
              :class="{ active: selectedImageCategory === '0' }"
              @click="selectImageCategory('0')"
            >
              全部
              <!-- Optional count display -->
            </div>
            <div
              v-for="category in imageCategoriesTree"
              :key="category.categoryId"
              class="category-item"
              :class="{
                active: selectedImageCategory === category.categoryId,
                'category-item-child': category.level > 0
              }"
              @click="selectImageCategory(category.categoryId)"
              :style="{ paddingLeft: 16 + category.level * 16 + 'px' }"
            >
              {{ category.name }}
              <span v-if="category.resourceCount" class="category-resource-count">
                {{ category.resourceCount }}
              </span>
            </div>
          </div>
        </div>
        <!-- End category sidebar -->

        <div class="picker-content">
          <div class="search-bar">
            <el-input
              v-model="searchQuery"
              placeholder="搜索图片名称"
              :prefix-icon="Search"
              clearable
              @change="handleSearchChange"
            />
          </div>

          <div v-loading="loadingImages" class="stickers-gallery">
            <el-empty v-if="!loadingImages && (!imageList || imageList.length === 0)" description="素材库中没有图片" />
            <div v-else class="stickers-grid">
              <div
                v-for="item in imageList"
                :key="item.mediaId || item.url"
                class="sticker-gallery-item"
                @click="selectSticker(item)"
              >
                <img :src="item.thumbUrl || item.url" class="gallery-image" :alt="item.name" />
                <div class="sticker-name">{{ item.name }}</div>
              </div>
            </div>
          </div>

          <div class="pagination-container">
            <el-pagination
              v-if="totalImages > 0"
              :current-page="currentPage"
              :page-size="pageSize"
              :page-sizes="[12, 24, 36, 48]"
              layout="total, sizes, prev, pager, next"
              :total="totalImages"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 保存为模板对话框 -->
    <el-dialog v-model="showSaveTemplateModal" title="保存贴纸模板" width="500px" destroy-on-close>
      <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="80px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" clearable />
        </el-form-item>
        <el-form-item label="模板备注" prop="description">
          <el-input
            v-model="templateForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板备注（可选）"
            clearable
          />
        </el-form-item>
        <el-form-item label="模板预览">
          <div class="template-preview">
            <div class="preview-info">
              <p><strong>贴纸名称：</strong>{{ localStickers[currentStickerIndex]?.name || '未命名贴纸' }}</p>
              <p><strong>图片ID：</strong>{{ localStickers[currentStickerIndex]?.ID }}</p>
            </div>
            <div class="sticker-summary" v-if="currentStickerIndex >= 0 && localStickers[currentStickerIndex]">
              <div class="sticker-summary-item">
                <span class="sticker-name">坐标和尺寸信息</span>
                <span class="sticker-details">
                  宽度: {{ localStickers[currentStickerIndex].Width?.toFixed(2) }}px | 高度:
                  {{ localStickers[currentStickerIndex].Height?.toFixed(2) }}px | X坐标:
                  {{ localStickers[currentStickerIndex].X?.toFixed(4) }} | Y坐标:
                  {{ localStickers[currentStickerIndex].Y?.toFixed(4) }}
                </span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSaveTemplateModal = false">取 消</el-button>
          <el-button type="primary" @click="saveTemplate" :loading="savingTemplate">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { Delete, Plus, Search, PictureRounded } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    getResourceLibrary,
    getResourceCategories,
    saveStickerTemplate,
    getStickerTemplates,
    deleteStickerTemplate
  } from '@/api/ai/auto-video'

  const props = defineProps({
    stickers: {
      type: Array,
      default: () => []
    },
    videoOutputWidth: {
      type: Number,
      default: 1080
    },
    videoOutputHeight: {
      type: Number,
      default: 1920
    }
  })

  const emit = defineEmits(['update:stickers'])

  const showDialog = ref(false)
  const showDrawer = ref(false)
  const localStickers = ref([])
  const tempStickers = ref([])
  const loadingImages = ref(false)
  const imageList = ref([])
  const searchQuery = ref('')
  const currentPage = ref(1)
  const pageSize = ref(24)
  const totalImages = ref(0)
  const imageCategories = ref([])
  const imageCategoriesTree = ref([])
  const selectedImageCategory = ref('0')
  const showSaveTemplateModal = ref(false)
  const templateForm = ref({})
  const templateRules = ref({})
  const savingTemplate = ref(false)
  const templateFormRef = ref(null)
  const currentStickerIndex = ref(-1)
  const stickerTemplates = ref([])
  const loadingTemplates = ref(false)
  const selectedTemplateIds = ref([])

  const confirmChanges = () => {
    console.log('[StickerSettings] confirmChanges 开始')
    const processedStickers = localStickers.value.map((sticker) => {
      console.log('[StickerSettings] 处理贴纸:', sticker.name, '原始sticker对象:', JSON.parse(JSON.stringify(sticker)))
      console.log(
        '[StickerSettings] 输入参数 - sticker.size:',
        sticker.size,
        '% | videoOutputWidth:',
        props.videoOutputWidth,
        'px | videoOutputHeight:',
        props.videoOutputHeight,
        'px'
      )
      console.log(
        '[StickerSettings] 输入参数 - sticker.originalWidth:',
        sticker.originalWidth,
        'px | sticker.originalHeight:',
        sticker.originalHeight,
        'px'
      )

      const sizeAsDecimal = sticker.size / 100 // 用户设置的大小百分比（例如 0.2 表示 20%）
      console.log('[StickerSettings] 计算值 - sizeAsDecimal:', sizeAsDecimal)

      // 优先使用已设置的坐标（如应用模板时设置的），否则根据position计算
      let finalX, finalY
      if (typeof sticker.X === 'number' && typeof sticker.Y === 'number') {
        // 使用已设置的精确坐标
        finalX = sticker.X
        finalY = sticker.Y
        console.log('[StickerSettings] 使用已设置的坐标 - X:', finalX, 'Y:', finalY)
      } else {
        // 根据position计算坐标
        const percentCoords = calculateCoordinates(sticker.position, sizeAsDecimal)
        finalX = percentCoords.X
        finalY = percentCoords.Y
        console.log('[StickerSettings] 根据position计算坐标 - percentCoords (X, Y 百分比):', percentCoords)
      }

      const stickerTargetWidthPx_final = sizeAsDecimal * props.videoOutputWidth
      console.log('[StickerSettings] 计算值 - stickerTargetWidthPx_final:', stickerTargetWidthPx_final, 'px')
      let stickerImageAspectRatio = 0

      // 优先使用已设置的尺寸（如应用模板时设置的），否则根据size计算
      let finalWidth, finalHeight
      if (typeof sticker.Width === 'number' && typeof sticker.Height === 'number') {
        // 使用已设置的精确尺寸
        finalWidth = sticker.Width
        finalHeight = sticker.Height
        console.log('[StickerSettings] 使用已设置的尺寸 - Width:', finalWidth, 'Height:', finalHeight)
      } else {
        // 根据size计算尺寸
        finalWidth = stickerTargetWidthPx_final
        if (sticker.originalWidth && sticker.originalHeight && sticker.originalWidth > 0) {
          stickerImageAspectRatio = sticker.originalHeight / sticker.originalWidth
          console.log('[StickerSettings] 计算值 - stickerImageAspectRatio (原始高/原始宽):', stickerImageAspectRatio)
          finalHeight = finalWidth * stickerImageAspectRatio
        } else {
          console.warn(
            '[StickerSettings] 警告: 贴纸',
            sticker.name,
            '缺少原始宽高信息或原始宽度为0，将按最终视频高度比例计算高度。'
          )
          finalHeight = sizeAsDecimal * props.videoOutputHeight
        }
        console.log('[StickerSettings] 根据size计算尺寸 - Width:', finalWidth, 'Height:', finalHeight)
      }

      const finalStickerObject = {
        ID: sticker.ID,
        mediaId: sticker.mediaId,
        mediaUrl: sticker.mediaUrl,
        name: sticker.name,
        thumbUrl: sticker.thumbUrl,
        position: sticker.position, // 保留原始的九宫格位置设置
        size: sticker.size, // 保留原始的大小百分比设置
        originalWidth: sticker.originalWidth, // 保留原始宽度信息
        originalHeight: sticker.originalHeight, // 保留原始高度信息

        X: finalX,
        Y: finalY,
        Width: parseFloat(finalWidth.toFixed(3)), // 存储最终视频的像素宽度
        Height: parseFloat(finalHeight.toFixed(3)) // 存储最终视频的像素高度
      }
      console.log('[StickerSettings] 处理后待提交的贴纸对象:', JSON.parse(JSON.stringify(finalStickerObject)))
      return finalStickerObject
    })
    emit('update:stickers', processedStickers)
    console.log(
      "[StickerSettings] confirmChanges 结束, emit('update:stickers') 数据:",
      JSON.parse(JSON.stringify(processedStickers))
    )
    showDialog.value = false
  }

  const cancelChanges = () => {
    localStickers.value = JSON.parse(JSON.stringify(tempStickers.value))
    showDialog.value = false
  }

  watch(showDialog, (val) => {
    if (val) {
      localStickers.value = props.stickers.map((s) => ({
        ...s,
        position: s.position || 'center',
        size: s.size || 20,
        coordinateX: s.X ? parseFloat((s.X * 100).toFixed(2)) : 0,
        coordinateY: s.Y ? parseFloat((s.Y * 100).toFixed(2)) : 0
      }))
      tempStickers.value = JSON.parse(JSON.stringify(localStickers.value))

      // 初始化模板选择状态
      selectedTemplateIds.value = new Array(localStickers.value.length).fill(null)

      // 不再在这里获取模板，改为在每个贴纸展开时按需获取
    }
  })

  watch(
    () => props.stickers,
    (newVal) => {
      if (!showDialog.value) {
        localStickers.value = newVal.map((s) => ({
          ...s,
          position: s.position || 'center',
          size: s.size || 20,
          coordinateX: s.X ? parseFloat((s.X * 100).toFixed(2)) : 0,
          coordinateY: s.Y ? parseFloat((s.Y * 100).toFixed(2)) : 0
        }))
      } else {
        // 如果对话框打开时stickers发生变化（比如从VideoPreview更新），也要同步更新
        const updatedStickers = newVal.map((s) => ({
          ...s,
          position: s.position || 'center',
          size: s.size || 20,
          coordinateX: s.X ? parseFloat((s.X * 100).toFixed(2)) : 0,
          coordinateY: s.Y ? parseFloat((s.Y * 100).toFixed(2)) : 0
        }))

        // 只更新存在的贴纸，保持当前正在编辑的状态
        updatedStickers.forEach((updatedSticker, index) => {
          if (localStickers.value[index] && localStickers.value[index].mediaId === updatedSticker.mediaId) {
            // 同步位置和大小，但保留其他可能正在编辑的属性，包括ID
            localStickers.value[index] = {
              ...localStickers.value[index],
              ...updatedSticker,
              position: updatedSticker.position,
              size: updatedSticker.size,
              X: updatedSticker.X,
              Y: updatedSticker.Y,
              Width: updatedSticker.Width,
              Height: updatedSticker.Height,
              coordinateX: updatedSticker.coordinateX,
              coordinateY: updatedSticker.coordinateY
            }
          }
        })
      }
    },
    { deep: true, immediate: true }
  )

  // 添加监听localStickers中size、position和坐标变化的watcher，实时同步到父组件
  watch(
    () =>
      localStickers.value.map((sticker) => ({
        size: sticker.size,
        position: sticker.position,
        coordinateX: sticker.coordinateX,
        coordinateY: sticker.coordinateY
      })),
    (newValues, oldValues) => {
      if (showDialog.value && oldValues) {
        // 检查是否真的有size、position或坐标发生变化
        const hasChanges = newValues.some((newVal, index) => {
          const oldVal = oldValues[index]
          return (
            oldVal &&
            (newVal.size !== oldVal.size ||
              newVal.position !== oldVal.position ||
              newVal.coordinateX !== oldVal.coordinateX ||
              newVal.coordinateY !== oldVal.coordinateY)
          )
        })

        if (hasChanges) {
          // 实时计算并同步数据到父组件，确保预览效果能立即更新
          const updatedStickers = localStickers.value.map((sticker) => {
            const sizeAsDecimal = sticker.size / 100

            // 优先使用用户输入的坐标，否则保留已设置的精确坐标，最后才根据position计算
            let finalX, finalY
            if (
              typeof sticker.coordinateX === 'number' &&
              typeof sticker.coordinateY === 'number' &&
              sticker.coordinateX >= 0 &&
              sticker.coordinateY >= 0
            ) {
              // 使用用户输入的百分比坐标，转换为小数
              finalX = sticker.coordinateX / 100
              finalY = sticker.coordinateY / 100
            } else if (
              typeof sticker.X === 'number' &&
              typeof sticker.Y === 'number' &&
              sticker.X >= 0 &&
              sticker.Y >= 0
            ) {
              // 保持已设置的精确坐标，只重新计算尺寸
              finalX = sticker.X
              finalY = sticker.Y
            } else {
              // 根据position计算坐标
              const percentCoords = calculateCoordinates(sticker.position, sizeAsDecimal)
              finalX = percentCoords.X
              finalY = percentCoords.Y
            }

            const stickerTargetWidthPx = sizeAsDecimal * props.videoOutputWidth

            // 优先保留已设置的精确尺寸，否则根据size计算
            let finalWidth, finalHeight
            if (
              typeof sticker.Width === 'number' &&
              typeof sticker.Height === 'number' &&
              sticker.Width > 0 &&
              sticker.Height > 0
            ) {
              // 如果已有精确尺寸但size变化了，需要按比例调整尺寸
              const currentSizePercent = (sticker.Width / props.videoOutputWidth) * 100
              if (Math.abs(currentSizePercent - sticker.size) > 0.1) {
                // size变化了，重新计算尺寸
                finalWidth = stickerTargetWidthPx
                if (sticker.originalWidth && sticker.originalHeight && sticker.originalWidth > 0) {
                  const stickerImageAspectRatio = sticker.originalHeight / sticker.originalWidth
                  finalHeight = stickerTargetWidthPx * stickerImageAspectRatio
                } else {
                  finalHeight = sizeAsDecimal * props.videoOutputHeight
                }
              } else {
                // size没有实质变化，保持现有尺寸
                finalWidth = sticker.Width
                finalHeight = sticker.Height
              }
            } else {
              // 根据size计算尺寸
              finalWidth = stickerTargetWidthPx
              if (sticker.originalWidth && sticker.originalHeight && sticker.originalWidth > 0) {
                const stickerImageAspectRatio = sticker.originalHeight / sticker.originalWidth
                finalHeight = stickerTargetWidthPx * stickerImageAspectRatio
              } else {
                finalHeight = sizeAsDecimal * props.videoOutputHeight
              }
            }

            return {
              ID: sticker.ID,
              mediaId: sticker.mediaId,
              mediaUrl: sticker.mediaUrl,
              name: sticker.name,
              thumbUrl: sticker.thumbUrl,
              position: sticker.position,
              size: sticker.size,
              originalWidth: sticker.originalWidth,
              originalHeight: sticker.originalHeight,
              X: finalX,
              Y: finalY,
              Width: parseFloat(finalWidth.toFixed(3)),
              Height: parseFloat(finalHeight.toFixed(3))
            }
          })

          emit('update:stickers', updatedStickers)
        }
      }
    },
    { deep: true }
  )

  const formatTooltip = (val) => {
    return val + '%'
  }

  const formatCoordinateTooltip = (val) => {
    return val.toFixed(1) + '%'
  }

  const removeSticker = (index) => {
    localStickers.value.splice(index, 1)
  }

  const openDrawer = () => {
    showDrawer.value = true
    searchQuery.value = ''
    currentPage.value = 1
    selectedImageCategory.value = '0'
    fetchImageCategories()
    fetchImages()
  }

  const fetchImages = async () => {
    loadingImages.value = true
    try {
      const params = {
        page: currentPage.value,
        pageSize: pageSize.value,
        keyword: searchQuery.value,
        type: 'image',
        ...(selectedImageCategory.value &&
          selectedImageCategory.value !== '0' && { category_id: selectedImageCategory.value })
      }
      const response = await getResourceLibrary(params)
      if (response.code === 0 && response.data) {
        imageList.value = (response.data.list || []).map((item) => {
          return {
            resourceId: String(item.ID || 0),
            mediaId: item.mediaId || '',
            name: item.name || '未命名',
            type: 'image',
            url: item.fileUrl || item.url || '',
            thumbUrl: item.thumbUrl || item.fileUrl || item.url || '',
            duration: item.duration || 0,
            width: item.width || 0,
            height: item.height || 0,
            categoryId: item.categoryId ? String(item.categoryId) : '',
            createdAt: item.CreatedAt || ''
          }
        })
        totalImages.value = response.data.total || 0
      } else {
        imageList.value = []
        totalImages.value = 0
        ElMessage.warning('获取图片素材失败: ' + (response.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取图片素材失败:', error)
      imageList.value = []
      totalImages.value = 0
      ElMessage.error('获取图片素材请求失败')
    } finally {
      loadingImages.value = false
    }
  }

  const fetchImageCategories = async () => {
    try {
      const res = await getResourceCategories('image')
      if (res.code === 0) {
        const categoriesData = res.data || []
        const flatCategories = []
        const allCategoriesForList = []

        const processAndFlatten = (categories, level = 0) => {
          if (!Array.isArray(categories)) {
            return
          }
          categories.forEach((cat) => {
            if (!cat) return
            const categoryItem = {
              categoryId: String(cat.ID || 0),
              name: cat.name || '未命名',
              pid: cat.pid || 0,
              resourceCount: cat.resourceCount || 0,
              level
            }
            flatCategories.push(categoryItem)

            allCategoriesForList.push({
              categoryId: String(cat.ID || 0),
              name: cat.name || '未命名',
              pid: cat.pid || 0,
              resourceCount: cat.resourceCount || 0
            })

            if (cat.children && Array.isArray(cat.children) && cat.children.length > 0) {
              processAndFlatten(cat.children, level + 1)
            }
          })
        }

        processAndFlatten(categoriesData)
        imageCategoriesTree.value = flatCategories

        allCategoriesForList.unshift({
          categoryId: '0',
          name: '全部',
          resourceCount: 0
        })

        imageCategories.value = allCategoriesForList
      } else {
        ElMessage.warning('获取图片分类失败: ' + (res.msg || '未知错误'))
        imageCategories.value = []
        imageCategoriesTree.value = []
      }
    } catch (error) {
      console.error('获取图片分类失败:', error)
      ElMessage.error('获取图片分类请求失败')
      imageCategories.value = []
      imageCategoriesTree.value = []
    }
  }

  const selectImageCategory = (categoryId) => {
    selectedImageCategory.value = categoryId
    currentPage.value = 1
    fetchImages()
  }

  const handleSearchChange = () => {
    currentPage.value = 1
    fetchImages()
  }

  const selectSticker = (imageFromList) => {
    console.log(
      '[StickerSettings] selectSticker - 开始选择贴纸, 素材库图片对象:',
      JSON.parse(JSON.stringify(imageFromList))
    )

    const exists = localStickers.value.some(
      (sticker) =>
        (sticker.mediaId && sticker.mediaId === imageFromList.mediaId && imageFromList.mediaId !== '') ||
        (!sticker.mediaId && sticker.mediaUrl === imageFromList.url)
    )

    if (exists) {
      ElMessage.warning('该图片已添加为贴纸')
      return
    }

    const img = new Image()
    img.onload = () => {
      const newSticker = {
        ID: parseInt(imageFromList.resourceId) || 0,
        mediaId: imageFromList.mediaId,
        mediaUrl: imageFromList.url,
        thumbUrl: imageFromList.thumbUrl || imageFromList.url,
        name: imageFromList.name,
        originalWidth: img.naturalWidth,
        originalHeight: img.naturalHeight,
        position: 'center',
        size: 20,
        coordinateX: 50, // 默认居中位置
        coordinateY: 50 // 默认居中位置
      }

      // 立即计算 X, Y, Width, Height 以便在预览中显示
      const sizeAsDecimal = newSticker.size / 100
      const percentCoords = calculateCoordinates(newSticker.position, sizeAsDecimal)

      const stickerTargetWidthPx = sizeAsDecimal * props.videoOutputWidth
      let stickerTargetHeightPx

      if (newSticker.originalWidth && newSticker.originalHeight && newSticker.originalWidth > 0) {
        const aspectRatio = newSticker.originalHeight / newSticker.originalWidth
        stickerTargetHeightPx = stickerTargetWidthPx * aspectRatio
      } else {
        stickerTargetHeightPx = sizeAsDecimal * props.videoOutputHeight
      }

      // 设置计算后的属性
      newSticker.X = percentCoords.X
      newSticker.Y = percentCoords.Y
      newSticker.Width = parseFloat(stickerTargetWidthPx.toFixed(3))
      newSticker.Height = parseFloat(stickerTargetHeightPx.toFixed(3))

      console.log(
        '[StickerSettings] selectSticker - 创建的新贴纸对象 (带真实宽高和计算坐标):',
        JSON.parse(JSON.stringify(newSticker))
      )

      localStickers.value.push(newSticker)

      // 立即同步到父组件，确保预览中能显示
      const processedStickers = localStickers.value.map((sticker) => {
        return {
          ID: sticker.ID,
          mediaId: sticker.mediaId,
          mediaUrl: sticker.mediaUrl,
          name: sticker.name,
          thumbUrl: sticker.thumbUrl,
          position: sticker.position,
          size: sticker.size,
          originalWidth: sticker.originalWidth,
          originalHeight: sticker.originalHeight,
          X: sticker.X,
          Y: sticker.Y,
          Width: sticker.Width,
          Height: sticker.Height
        }
      })
      emit('update:stickers', processedStickers)

      // 自动获取该贴纸的模板
      if (newSticker.ID) {
        fetchStickerTemplates(newSticker.ID)
      }

      showDrawer.value = false
    }
    img.onerror = () => {
      console.error(`[StickerSettings] selectSticker - 图片 ${imageFromList.name} 加载失败: ${imageFromList.url}`)
      ElMessage.error(`图片 ${imageFromList.name} 加载失败，无法获取原始尺寸，将使用默认尺寸。`)

      const newSticker = {
        ID: parseInt(imageFromList.resourceId) || 0,
        mediaId: imageFromList.mediaId,
        mediaUrl: imageFromList.url,
        thumbUrl: imageFromList.thumbUrl || imageFromList.url,
        name: imageFromList.name,
        originalWidth: imageFromList.width || 0,
        originalHeight: imageFromList.height || 0,
        position: 'center',
        size: 20,
        coordinateX: 50, // 默认居中位置
        coordinateY: 50 // 默认居中位置
      }

      // 立即计算 X, Y, Width, Height 以便在预览中显示
      const sizeAsDecimal = newSticker.size / 100
      const percentCoords = calculateCoordinates(newSticker.position, sizeAsDecimal)

      const stickerTargetWidthPx = sizeAsDecimal * props.videoOutputWidth
      let stickerTargetHeightPx

      if (newSticker.originalWidth && newSticker.originalHeight && newSticker.originalWidth > 0) {
        const aspectRatio = newSticker.originalHeight / newSticker.originalWidth
        stickerTargetHeightPx = stickerTargetWidthPx * aspectRatio
      } else {
        stickerTargetHeightPx = sizeAsDecimal * props.videoOutputHeight
      }

      // 设置计算后的属性
      newSticker.X = percentCoords.X
      newSticker.Y = percentCoords.Y
      newSticker.Width = parseFloat(stickerTargetWidthPx.toFixed(3))
      newSticker.Height = parseFloat(stickerTargetHeightPx.toFixed(3))

      localStickers.value.push(newSticker)

      // 立即同步到父组件，确保预览中能显示
      const processedStickers = localStickers.value.map((sticker) => {
        return {
          ID: sticker.ID,
          mediaId: sticker.mediaId,
          mediaUrl: sticker.mediaUrl,
          name: sticker.name,
          thumbUrl: sticker.thumbUrl,
          position: sticker.position,
          size: sticker.size,
          originalWidth: sticker.originalWidth,
          originalHeight: sticker.originalHeight,
          X: sticker.X,
          Y: sticker.Y,
          Width: sticker.Width,
          Height: sticker.Height
        }
      })
      emit('update:stickers', processedStickers)

      // 自动获取该贴纸的模板
      if (newSticker.ID) {
        fetchStickerTemplates(newSticker.ID)
      }

      showDrawer.value = false
    }

    if (!imageFromList.url) {
      console.error(
        '[StickerSettings] selectSticker - 选中的图片素材缺少有效的url:',
        JSON.parse(JSON.stringify(imageFromList))
      )
      ElMessage.error('选中的图片素材缺少有效URL，无法添加。')
      return
    }
    img.src = imageFromList.url
    console.log('[StickerSettings] selectSticker - 开始加载图片获取尺寸: ', img.src)
  }

  const handleSizeChange = (val) => {
    pageSize.value = val
    currentPage.value = 1
    fetchImages()
  }

  const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchImages()
  }

  function calculateCoordinates(position, sizePercent) {
    const width = sizePercent
    const height = sizePercent

    let X, Y

    switch (position) {
      case 'topLeft':
        X = 0
        Y = 0
        break
      case 'topCenter':
        X = 0.5 - width / 2
        Y = 0
        break
      case 'topRight':
        X = 1 - width
        Y = 0
        break
      case 'centerLeft':
        X = 0
        Y = 0.5 - height / 2
        break
      case 'center':
        X = 0.5 - width / 2
        Y = 0.5 - height / 2
        break
      case 'centerRight':
        X = 1 - width
        Y = 0.5 - height / 2
        break
      case 'bottomLeft':
        X = 0
        Y = 1 - height
        break
      case 'bottomCenter':
        X = 0.5 - width / 2
        Y = 1 - height
        break
      case 'bottomRight':
        X = 1 - width
        Y = 1 - height
        break
      default:
        X = 0.5 - width / 2
        Y = 0.5 - height / 2
    }

    X = Math.max(-0.01, Math.min(1.01 - width, X))
    Y = Math.max(-0.01, Math.min(1.01 - height, Y))

    return {
      X: parseFloat(X.toFixed(3)),
      Y: parseFloat(Y.toFixed(3)),
      Width: parseFloat(width.toFixed(3)),
      Height: parseFloat(height.toFixed(3))
    }
  }

  function calculatePositionFromCoordinates(x, y, widthPercent, heightPercent) {
    // 容差值，用于判断位置
    const tolerance = 0.05

    // 判断水平位置
    let horizontalPos = 'center'
    if (Math.abs(x) < tolerance) {
      horizontalPos = 'left'
    } else if (Math.abs(x + widthPercent - 1) < tolerance) {
      horizontalPos = 'right'
    }

    // 判断垂直位置
    let verticalPos = 'center'
    if (Math.abs(y) < tolerance) {
      verticalPos = 'top'
    } else if (Math.abs(y + heightPercent - 1) < tolerance) {
      verticalPos = 'bottom'
    }

    // 组合位置
    if (verticalPos === 'top') {
      if (horizontalPos === 'left') return 'topLeft'
      if (horizontalPos === 'right') return 'topRight'
      return 'topCenter'
    } else if (verticalPos === 'bottom') {
      if (horizontalPos === 'left') return 'bottomLeft'
      if (horizontalPos === 'right') return 'bottomRight'
      return 'bottomCenter'
    } else {
      if (horizontalPos === 'left') return 'centerLeft'
      if (horizontalPos === 'right') return 'centerRight'
      return 'center'
    }
  }

  const getDisplayPixelWidth = (sticker) => {
    if (!sticker || typeof sticker.size !== 'number' || !props.videoOutputWidth) {
      return ''
    }
    const sizeAsDecimal = sticker.size / 100
    const pixelWidth = sizeAsDecimal * props.videoOutputWidth
    return parseFloat(pixelWidth.toFixed(1))
  }

  const getDisplayPixelHeight = (sticker) => {
    if (!sticker || typeof sticker.size !== 'number' || !props.videoOutputWidth || !props.videoOutputHeight) {
      return ''
    }
    const displayPixelWidth = getDisplayPixelWidth(sticker)
    if (displayPixelWidth === '') return ''

    if (sticker.originalWidth && sticker.originalHeight && sticker.originalWidth > 0) {
      const aspectRatio = sticker.originalHeight / sticker.originalWidth
      const pixelHeight = displayPixelWidth * aspectRatio
      return parseFloat(pixelHeight.toFixed(1))
    } else {
      const sizeAsDecimal = sticker.size / 100
      const fallbackPixelHeight = sizeAsDecimal * props.videoOutputHeight
      return parseFloat(fallbackPixelHeight.toFixed(1)) + ' (估算)'
    }
  }

  const showStickerTemplateDialog = (index) => {
    showSaveTemplateModal.value = true

    // 移除图片名称中的文件后缀
    const stickerName = localStickers.value[index].name || '未命名贴纸'
    const nameWithoutExtension = stickerName.replace(/\.(jpg|jpeg|png|gif|bmp|webp)$/i, '')

    templateForm.value = {
      name: nameWithoutExtension,
      description: ''
    }
    templateRules.value = {
      name: [
        { required: true, message: '请输入模板名称', trigger: 'blur' },
        { min: 1, max: 50, message: '模板名称长度在 1 到 50 个字符', trigger: 'blur' }
      ]
    }
    currentStickerIndex.value = index
  }

  const saveTemplate = async () => {
    try {
      // 先验证表单
      await templateFormRef.value.validate()

      savingTemplate.value = true

      // 构建模板数据
      const currentSticker = localStickers.value[currentStickerIndex.value]
      const templateData = {
        name: templateForm.value.name,
        description: templateForm.value.description,
        imageId: currentSticker.ID,
        width: currentSticker.Width,
        height: currentSticker.Height,
        x: currentSticker.X,
        y: currentSticker.Y
      }

      console.log('保存模板数据:', templateData)

      // 调用API保存模板
      await saveStickerTemplate(templateData)
      ElMessage.success('模板保存成功！')

      showSaveTemplateModal.value = false
    } catch (error) {
      if (error.message && error.message.includes('validate')) {
        // 表单验证失败，不需要额外处理
        return
      }
      console.error('保存模板失败:', error)
      ElMessage.error('保存模板失败')
    } finally {
      savingTemplate.value = false
    }
  }

  const applyTemplate = (index, templateId) => {
    if (!templateId) return

    const template = stickerTemplates.value.find((t) => t.ID === templateId)
    if (!template) {
      ElMessage.error('模板不存在')
      return
    }

    // 从模板数据计算size百分比
    const templateSizePercent = Math.round((template.width / props.videoOutputWidth) * 100)

    // 根据坐标反推position位置
    const templatePosition = calculatePositionFromCoordinates(
      template.x,
      template.y,
      template.width / props.videoOutputWidth,
      template.height / props.videoOutputHeight
    )

    // 应用模板数据到当前贴纸，更新界面显示值
    localStickers.value[index] = {
      ...localStickers.value[index],
      size: templateSizePercent,
      position: templatePosition,
      Width: template.width,
      Height: template.height,
      X: template.x,
      Y: template.y,
      coordinateX: parseFloat((template.x * 100).toFixed(2)),
      coordinateY: parseFloat((template.y * 100).toFixed(2))
    }

    // 立即同步数据到父组件，确保预览中的坐标也能同时更新
    const updatedStickers = localStickers.value.map((sticker) => {
      const sizeAsDecimal = sticker.size / 100
      const percentCoords = calculateCoordinates(sticker.position, sizeAsDecimal)

      let stickerTargetWidthPx = sizeAsDecimal * props.videoOutputWidth
      let stickerTargetHeightPx

      if (sticker.originalWidth && sticker.originalHeight && sticker.originalWidth > 0) {
        const aspectRatio = sticker.originalHeight / sticker.originalWidth
        stickerTargetHeightPx = stickerTargetWidthPx * aspectRatio
      } else {
        stickerTargetHeightPx = sizeAsDecimal * props.videoOutputHeight
      }

      return {
        ID: sticker.ID,
        mediaId: sticker.mediaId,
        mediaUrl: sticker.mediaUrl,
        name: sticker.name,
        thumbUrl: sticker.thumbUrl,
        position: sticker.position,
        size: sticker.size,
        originalWidth: sticker.originalWidth,
        originalHeight: sticker.originalHeight,
        X: sticker.X || percentCoords.X,
        Y: sticker.Y || percentCoords.Y,
        Width: sticker.Width || parseFloat(stickerTargetWidthPx.toFixed(3)),
        Height: sticker.Height || parseFloat(stickerTargetHeightPx.toFixed(3))
      }
    })

    emit('update:stickers', updatedStickers)

    ElMessage.success(`已应用模板: ${template.name}`)
  }

  const fetchStickerTemplates = async (imageId = null) => {
    loadingTemplates.value = true
    try {
      const params = {
        page: 1,
        pageSize: 100 // 获取所有模板
      }

      // 如果提供了imageId，则添加到参数中
      if (imageId) {
        params.imageId = imageId
      }

      const response = await getStickerTemplates(params)

      if (response.code === 0 && response.data) {
        const newTemplates = response.data.list || []

        if (imageId) {
          // 如果是特定图片的请求，先移除该图片的旧模板，然后添加新模板
          stickerTemplates.value = stickerTemplates.value.filter((t) => t.imageId !== imageId)
          stickerTemplates.value.push(...newTemplates)
          ElMessage.success(`已加载图片 ID ${imageId} 的模板`)
        } else {
          // 如果是获取所有模板，直接替换
          stickerTemplates.value = newTemplates
          ElMessage.success('模板列表已刷新')
        }
      } else {
        ElMessage.error('获取模板列表失败: ' + (response.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取模板列表失败:', error)
      ElMessage.error('获取模板列表失败')
    } finally {
      loadingTemplates.value = false
    }
  }

  const deleteTemplate = async (template) => {
    try {
      await ElMessageBox.confirm(`确定要删除模板 "${template.name}" 吗？此操作不可恢复。`, '删除模板', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 调用删除API
      await deleteStickerTemplate(template)
      ElMessage.success('模板删除成功')

      // 刷新模板列表
      await fetchStickerTemplates()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除模板失败:', error)
        ElMessage.error('删除模板失败')
      }
    }
  }

  const shouldShowTemplates = (imageId) => {
    return stickerTemplates.value.some((t) => t.imageId === imageId)
  }

  const getTemplatesForSticker = (imageId) => {
    return stickerTemplates.value.filter((t) => t.imageId === imageId)
  }

  const isImageLoaded = (imageId) => {
    return stickerTemplates.value.some((t) => t.imageId === imageId)
  }

  const handleCoordinateInput = (index, coordinate, value) => {
    // 处理坐标输入，确保值的合理性
    const numericValue = parseFloat(value) || 0

    // 限制在0-100范围内
    const clampedValue = Math.max(0, Math.min(100, numericValue))

    // 更新对应的坐标字段
    if (coordinate === 'X') {
      localStickers.value[index].coordinateX = clampedValue
    } else if (coordinate === 'Y') {
      localStickers.value[index].coordinateY = clampedValue
    }
  }

  const validateCoordinate = (index, coordinate) => {
    // 验证坐标值并确保在合理范围内
    const sticker = localStickers.value[index]
    const field = coordinate === 'X' ? 'coordinateX' : 'coordinateY'
    const coordinateValue = sticker[field]

    if (coordinateValue < 0 || coordinateValue > 100 || isNaN(coordinateValue)) {
      ElMessage.warning(`${coordinate}坐标必须在0到100之间`)
      // 重置为合理值
      if (coordinateValue < 0) {
        sticker[field] = 0
      } else if (coordinateValue > 100) {
        sticker[field] = 100
      } else if (isNaN(coordinateValue)) {
        sticker[field] = coordinate === 'X' ? (sticker.X ? sticker.X * 100 : 0) : sticker.Y ? sticker.Y * 100 : 0
      }
    }
  }
</script>

<style scoped>
  .setting-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s;
  }

  .setting-item:hover {
    background-color: #f5f7fa;
  }

  .setting-item .el-icon {
    font-size: 24px;
    color: #409eff;
    margin-bottom: 5px;
  }

  .popover-content {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .sticker-list {
    margin-bottom: 20px;
  }

  .sticker-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    background-color: #fdfdfd;
    gap: 20px;
  }

  .sticker-preview {
    position: relative;
    width: 120px;
    height: 120px;
    border: 1px dashed #dcdfe6;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
  }

  .sticker-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .sticker-placeholder {
    color: #c0c4cc;
    font-size: 13px;
  }

  .sticker-controls {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    opacity: 0.8;
    transition: opacity 0.2s;
  }

  .sticker-item:hover .sticker-controls {
    opacity: 1;
  }

  .sticker-config {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .position-label,
  .size-label {
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    margin-right: 10px;
    display: inline-block;
    width: 60px;
    text-align: right;
  }

  .sticker-position .el-radio-group,
  .sticker-size .el-slider {
    flex: 1;
  }

  .add-sticker {
    margin-top: 20px;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
  }

  .sticker-picker {
    display: flex;
    height: 100%;
  }

  .picker-sidebar {
    width: 200px;
    border-right: 1px solid #ebeef5;
    overflow-y: auto;
    flex-shrink: 0;
    background-color: #fafafa;
  }

  .category-list {
    display: flex;
    flex-direction: column;
    padding: 10px 0;
  }

  .category-item {
    padding: 10px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
    color: #303133;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .category-item:hover {
    background-color: #ecf5ff;
  }

  .category-item.active {
    background-color: #e1f3d8;
    border-left-color: #67c23a;
    color: #67c23a;
  }

  .category-item-child {
    font-size: 13px;
    color: #606266;
  }

  .category-item-child:hover {
    background-color: #dcdfe6;
  }

  .category-resource-count {
    background-color: #f0f2f5;
    color: #909399;
    font-size: 12px;
    line-height: 1;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
  }

  .category-item.active .category-resource-count {
    background-color: #d8e6fd;
    color: #409eff;
  }

  .picker-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 15px 20px;
    overflow: hidden;
  }

  .search-bar {
    margin-bottom: 15px;
    flex-shrink: 0;
  }

  .stickers-gallery {
    flex: 1;
    overflow-y: auto;
    margin: 0 -5px;
    padding: 5px;
  }

  .stickers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 15px;
  }

  .sticker-gallery-item {
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-align: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 150px;
  }

  .sticker-gallery-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #a0cfff;
  }

  .gallery-image {
    width: 100%;
    height: 90px;
    object-fit: contain;
    margin-bottom: 8px;
  }

  .sticker-name {
    font-size: 13px;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    margin-top: auto;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
    flex-shrink: 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 10px 20px;
    border-top: 1px solid #f0f0f0;
  }

  .el-empty {
    padding: 30px 0;
  }

  .stickers-gallery[v-loading='true'] {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }

  .sticker-dimension-row {
    display: flex;
    gap: 15px;
    align-items: center;
  }

  .sticker-dimension-display-item {
    display: flex;
    align-items: center;
    gap: 5px;
    flex: 1;
  }

  .sticker-dimension-display-item .dimension-label {
    font-size: 14px;
    color: #606266;
    margin-right: 0;
  }

  .sticker-dimension-display-item .el-input {
    flex-grow: 1;
  }

  .sticker-coordinate-row {
    display: flex;
    gap: 15px;
    align-items: flex-start;
  }

  .sticker-coordinate-display-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
  }

  .sticker-coordinate-display-item .coordinate-label {
    font-size: 14px;
    color: #606266;
    margin-right: 0;
    font-weight: 500;
  }

  .sticker-coordinate-display-item .el-input {
    width: 100%;
  }

  .coordinate-slider-container {
    width: 100%;
    padding: 0 2px;
  }

  .sticker-template-actions {
    margin-top: 10px;
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .template-selector {
    display: flex;
    flex-direction: column;
    margin-bottom: 5px;
  }

  .template-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    width: 100%;
  }

  .template-label {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }

  .template-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background-color: #fafafa;
    margin-bottom: 10px;
  }

  .template-item {
    padding: 12px;
    border-bottom: 1px solid #e4e7ed;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .template-item:last-child {
    border-bottom: none;
  }

  .template-item:hover {
    background-color: #ecf5ff;
  }

  .template-selected {
    background-color: #e1f3d8 !important;
    border-left: 3px solid #67c23a;
  }

  .template-content {
    flex: 1;
  }

  .template-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .template-description {
    font-size: 12px;
    color: #909399;
    margin-bottom: 6px;
    line-height: 1.4;
  }

  .template-details {
    font-size: 11px;
    color: #c0c4cc;
    font-family: 'Monaco', 'Consolas', monospace;
  }

  .template-actions {
    flex-shrink: 0;
    margin-left: 10px;
  }

  .template-empty {
    padding: 20px;
    text-align: center;
  }

  .template-preview {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8f9fa;
  }

  .preview-info {
    margin-bottom: 15px;
  }

  .preview-info p {
    margin: 5px 0;
    font-size: 14px;
    color: #606266;
  }

  .sticker-summary {
    width: 100%;
  }

  .sticker-summary-item {
    margin-bottom: 8px;
    padding: 8px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #ffffff;
  }

  .sticker-summary .sticker-name {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 3px;
  }

  .sticker-details {
    font-size: 12px;
    color: #909399;
  }
</style>
