<template>
  <div class="imagetext-preview">
    <div class="preview-container">
      <!-- 预览区域 -->
      <div class="preview-canvas" ref="previewCanvas">
        <div v-if="!hasValidContent" class="preview-placeholder">
          <el-icon class="placeholder-icon"><Picture /></el-icon>
          <p>暂无预览内容</p>
          <p class="placeholder-tip">请先添加图片素材</p>
        </div>

        <div v-else class="preview-content">
          <!-- 图片预览 -->
          <div v-if="previewImage" class="image-container">
            <img :src="previewImage.url" alt="预览图片" class="preview-image" />

            <!-- 标题叠加层 -->
            <div
              v-for="(title, index) in filteredTitles"
              :key="title.id || index"
              class="title-overlay"
              :style="getTitleStyle(title)"
            >
              {{ title.content }}
            </div>

            <!-- 贴纸叠加层 -->
            <div
              v-for="(sticker, index) in filteredStickers"
              :key="sticker.id || index"
              class="sticker-overlay"
              :style="getStickerStyle(sticker)"
            >
              <img v-if="sticker.mediaUrl" :src="sticker.mediaUrl" alt="贴纸" />
            </div>
          </div>
        </div>
      </div>

      <!-- 预览控制 -->
      <div v-if="validImageCount > 0" class="preview-controls">
        <!-- 分组切换 -->
        <div class="group-selector">
          <el-button size="small" :disabled="validImageCount === 0 || currentImageIndex === 0" @click="previousGroup">
            <el-icon><ArrowLeft /></el-icon>
            上一组
          </el-button>

          <div class="group-buttons">
            <el-button
              v-for="(image, index) in (props.imageMaterials || []).filter((img) => img.mediaUrl)"
              :key="index"
              :type="currentImageIndex === index ? 'primary' : 'default'"
              size="small"
              @click="setCurrentGroup(index)"
            >
              分组 {{ index + 1 }}
            </el-button>
          </div>

          <el-button
            size="small"
            :disabled="validImageCount === 0 || currentImageIndex === validImageCount - 1"
            @click="nextGroup"
          >
            下一组
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>

        <!-- 分组内图片切换 -->
        <div class="image-selector">
          <el-button
            size="small"
            :disabled="currentGroupImageCount <= 1 || currentImageIndexInGroup === 0"
            @click="previousImage"
          >
            <el-icon><ArrowLeft /></el-icon>
            上一张
          </el-button>

          <div class="image-info">
            <span>第 {{ currentImageIndexInGroup + 1 }} 张 / 共 {{ currentGroupImageCount }} 张</span>
          </div>

          <el-button
            size="small"
            :disabled="currentGroupImageCount <= 1 || currentImageIndexInGroup === currentGroupImageCount - 1"
            @click="nextImage"
          >
            下一张
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>

        <div class="preview-info">
          <el-descriptions :column="1" size="small" border>
            <el-descriptions-item label="当前分组">
              {{ currentImageIndex + 1 }} / {{ validImageCount }}
            </el-descriptions-item>
            <el-descriptions-item label="分组内图片">
              {{ currentImageIndexInGroup + 1 }} / {{ currentGroupImageCount }}
            </el-descriptions-item>
            <el-descriptions-item label="标题数量">
              {{ filteredTitles.length }}
            </el-descriptions-item>
            <el-descriptions-item label="贴纸数量">
              {{ filteredStickers.length }}
            </el-descriptions-item>
            <el-descriptions-item label="生成数量">
              {{ otherSettings.generateCount || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="图片数量">
              {{ otherSettings.minImageCount || 3 }} - {{ otherSettings.maxImageCount || 9 }} 张
            </el-descriptions-item>
          </el-descriptions>

          <!-- 临时调试信息，确认修复效果 -->
          <div
            style="
              margin-top: 10px;
              padding: 8px;
              background: #f0f9ff;
              border-radius: 4px;
              font-size: 12px;
              color: #0369a1;
            "
          >
            <strong>调试：</strong>当前分组有{{ currentGroupImageCount }}张图片， 正在显示第{{
              currentImageIndexInGroup + 1
            }}张
          </div>
        </div>
      </div>

      <!-- 设置提示 -->
      <div class="preview-tips">
        <el-alert type="info" :closable="false" show-icon style="margin-top: 15px">
          <template #title> 预览说明 </template>
          <ul style="margin: 0; padding-left: 20px">
            <li>当前预览展示选中分组的图片效果</li>
            <li>实际生成时会根据设置从各分组中选择图片组合</li>
            <li>标题和贴纸会根据应用模式添加到对应图片</li>
          </ul>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue'
  import { Picture, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
  import { fontList, injectFontCSS, preloadAllFonts } from '../videoEffects.js'

  const props = defineProps({
    imageMaterials: {
      type: Array,
      default: () => []
    },
    titleSettings: {
      type: Array,
      default: () => []
    },
    stickerSettings: {
      type: Array,
      default: () => []
    },
    activeTitleIndex: {
      type: Number,
      default: -1
    },
    otherSettings: {
      type: Object,
      default: () => ({})
    }
  })

  const previewCanvas = ref(null)
  const currentImageIndex = ref(0) // 当前分组索引
  const currentImageIndexInGroup = ref(0) // 当前分组内的图片索引

  // 计算属性
  const hasValidContent = computed(() => {
    return (props.imageMaterials || []).some((img) => img.mediaUrl)
  })

  const validImageCount = computed(() => {
    return (props.imageMaterials || []).filter((img) => img.mediaUrl).length
  })

  // 当前分组内的图片数量
  const currentGroupImageCount = computed(() => {
    const validImages = (props.imageMaterials || []).filter((img) => img.mediaUrl)
    if (validImages.length === 0) return 0

    const currentGroup = validImages[currentImageIndex.value]
    if (!currentGroup) return 0

    // 计算当前分组的图片总数：主图片(1张) + 额外图片数量
    let count = 0

    // 主图片
    if (currentGroup.mediaUrl) {
      count += 1
    }

    // 额外图片
    if (currentGroup.extraMedia && Array.isArray(currentGroup.extraMedia)) {
      count += currentGroup.extraMedia.length
    }

    return count
  })

  const previewImage = computed(() => {
    const validImages = (props.imageMaterials || []).filter((img) => img.mediaUrl)
    if (validImages.length === 0) return null

    const groupIndex = Math.min(currentImageIndex.value, validImages.length - 1)
    const currentGroup = validImages[groupIndex]
    if (!currentGroup) return null

    // 根据分组内图片索引获取具体图片
    const imageIndexInGroup = currentImageIndexInGroup.value

    // 第0张是主图片
    if (imageIndexInGroup === 0 && currentGroup.mediaUrl) {
      return {
        url: currentGroup.mediaUrl,
        name: currentGroup.name || '主图片',
        ...currentGroup
      }
    }

    // 第1张开始是额外图片
    if (imageIndexInGroup > 0 && currentGroup.extraMedia && Array.isArray(currentGroup.extraMedia)) {
      const extraIndex = imageIndexInGroup - 1
      if (extraIndex < currentGroup.extraMedia.length) {
        const extraImage = currentGroup.extraMedia[extraIndex]
        return {
          url: extraImage.url,
          name: extraImage.name || `额外图片${extraIndex + 1}`,
          ...extraImage
        }
      }
    }

    // 默认返回主图片
    return {
      url: currentGroup.mediaUrl,
      name: currentGroup.name || '主图片',
      ...currentGroup
    }
  })

  const filteredTitles = computed(() => {
    return (props.titleSettings || []).filter((title) => title.content && title.content.trim())
  })

  const filteredStickers = computed(() => {
    return (props.stickerSettings || []).filter((sticker) => sticker.mediaUrl)
  })

  // 方法
  const setCurrentGroup = (index) => {
    currentImageIndex.value = index
    currentImageIndexInGroup.value = 0 // 切换分组时重置分组内图片索引
  }

  const previousGroup = () => {
    if (currentImageIndex.value > 0) {
      currentImageIndex.value--
      currentImageIndexInGroup.value = 0 // 切换分组时重置分组内图片索引
    }
  }

  const nextGroup = () => {
    if (currentImageIndex.value < validImageCount.value - 1) {
      currentImageIndex.value++
      currentImageIndexInGroup.value = 0 // 切换分组时重置分组内图片索引
    }
  }

  // 分组内图片切换方法
  const previousImage = () => {
    console.log('切换上一张图片', {
      currentIndex: currentImageIndexInGroup.value,
      totalCount: currentGroupImageCount.value
    })
    if (currentImageIndexInGroup.value > 0) {
      currentImageIndexInGroup.value--
      console.log('切换到:', currentImageIndexInGroup.value)
    }
  }

  const nextImage = () => {
    console.log('切换下一张图片', {
      currentIndex: currentImageIndexInGroup.value,
      totalCount: currentGroupImageCount.value
    })
    if (currentImageIndexInGroup.value < currentGroupImageCount.value - 1) {
      currentImageIndexInGroup.value++
      console.log('切换到:', currentImageIndexInGroup.value)
    }
  }

  // 获取字体的CSS名称
  const getFontCSSName = (fontUrl) => {
    if (!fontUrl) return 'inherit'

    const fontItem = fontList.find((font) => font.value === fontUrl)
    if (fontItem) {
      return `'${fontItem.fontFamily}', sans-serif`
    }

    return 'inherit'
  }

  const getTitleStyle = (title) => {
    // 缩小预览字体以匹配实际生成效果
    // 预览容器相对较小，需要缩小字体来模拟实际图片上的视觉效果
    const baseFontSize = title.fontSize || 20
    const scaledFontSize = Math.max(baseFontSize * 0.6, 12) // 缩小到60%，最小12px

    const style = {
      fontSize: `${scaledFontSize}px`,
      color: title.fontStyle?.color || '#ffffff',
      fontFamily: getFontCSSName(title.fontFamily),
      top: `${(title.height || 0.15) * 100}%`
    }

    // 处理水平对齐
    if (title.alignment === 'left') {
      style.left = '20px'
      style.transform = 'none'
    } else if (title.alignment === 'right') {
      style.right = '20px'
      style.left = 'auto'
      style.transform = 'none'
    } else {
      // center 对齐 (默认)
      style.left = '50%'
      style.transform = 'translateX(-50%)'
    }

    // 根据字体样式类型设置样式
    if (title.fontStyle?.styleType === 'background') {
      style.backgroundColor = title.fontStyle.backgroundColor || 'rgba(0, 0, 0, 0.5)'
      style.padding = '8px 12px'
      style.borderRadius = '4px'
    } else if (title.fontStyle?.styleType === 'border') {
      style.textShadow = `
        -${title.fontStyle.borderWidth || 1}px -${title.fontStyle.borderWidth || 1}px 0 ${
        title.fontStyle.borderColor || '#000000'
      },
        ${title.fontStyle.borderWidth || 1}px -${title.fontStyle.borderWidth || 1}px 0 ${
        title.fontStyle.borderColor || '#000000'
      },
        -${title.fontStyle.borderWidth || 1}px ${title.fontStyle.borderWidth || 1}px 0 ${
        title.fontStyle.borderColor || '#000000'
      },
        ${title.fontStyle.borderWidth || 1}px ${title.fontStyle.borderWidth || 1}px 0 ${
        title.fontStyle.borderColor || '#000000'
      }
      `
    }

    return style
  }

  const getStickerStyle = (sticker) => {
    const x = sticker.x !== undefined ? sticker.x : 0.5 // 默认居中
    const y = sticker.y !== undefined ? sticker.y : 0.5 // 默认居中
    const scale = sticker.scale || 1
    const rotation = sticker.rotation || 0

    // 使用百分比定位，基于中心点
    // x, y 是 0-1 的相对坐标，转换为百分比
    const style = {
      position: 'absolute',
      left: `${x * 100}%`,
      top: `${y * 100}%`,
      transform: `translate(-50%, -50%) scale(${scale}) rotate(${rotation}deg)`,
      opacity: sticker.opacity !== undefined ? sticker.opacity : 1
    }

    return style
  }

  // 监听图片材料变化，重置当前图片索引
  watch(
    () => props.imageMaterials,
    (newMaterials) => {
      const validCount = newMaterials.filter((img) => img.mediaUrl).length
      if (currentImageIndex.value >= validCount) {
        currentImageIndex.value = Math.max(0, validCount - 1)
        currentImageIndexInGroup.value = 0
      }
    },
    { deep: true }
  )

  // 监听当前分组变化，重置分组内图片索引
  watch(
    () => currentImageIndex.value,
    () => {
      currentImageIndexInGroup.value = 0
    }
  )

  // 监听分组内图片数量变化，防止索引越界
  watch(
    () => currentGroupImageCount.value,
    (newCount) => {
      if (currentImageIndexInGroup.value >= newCount) {
        currentImageIndexInGroup.value = Math.max(0, newCount - 1)
      }
    }
  )

  // 在组件挂载时加载字体
  onMounted(async () => {
    try {
      injectFontCSS()
      await preloadAllFonts()
      console.log('ImageTextPreview: 字体CSS注入和预加载完成')
    } catch (error) {
      console.warn('ImageTextPreview: 字体加载失败:', error)
    }
  })
</script>

<style scoped>
  .imagetext-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .preview-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    color: #333;
    border-bottom: 2px solid #4b6cb7;
    padding-bottom: 10px;
  }

  .preview-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .preview-canvas {
    flex: 1;
    min-height: 300px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .preview-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    text-align: center;
  }

  .placeholder-icon {
    font-size: 48px;
    margin-bottom: 15px;
    color: #ccc;
  }

  .placeholder-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }

  .preview-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  }

  .image-container {
    position: relative;
    display: inline-block;
    max-width: 100%;
    max-height: 100%;
  }

  .preview-image {
    display: block;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
  }

  .title-overlay {
    position: absolute;
    font-weight: 500;
    white-space: nowrap;
    max-width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
    pointer-events: none;
    width: max-content;
  }

  .sticker-overlay {
    position: absolute;
    pointer-events: none;
  }

  .sticker-overlay img {
    width: auto;
    height: auto;
    max-width: 100px;
    max-height: 100px;
    object-fit: contain;
  }

  /* 贴纸位置样式 - 现在通过 getStickerStyle 函数动态设置 */

  .preview-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .group-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
  }

  .group-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .image-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: center;
  }

  .image-info {
    padding: 8px 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    min-width: 120px;
    text-align: center;
  }

  .preview-info {
    background-color: #fff;
    border-radius: 6px;
    padding: 10px;
  }

  .preview-tips {
    margin-top: auto;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .preview-canvas {
      min-height: 250px;
    }

    .group-selector {
      flex-direction: column;
      gap: 10px;
    }

    .group-buttons {
      justify-content: flex-start;
    }

    .image-selector {
      flex-direction: column;
      gap: 10px;
    }

    .image-info {
      min-width: auto;
    }
  }
</style>
